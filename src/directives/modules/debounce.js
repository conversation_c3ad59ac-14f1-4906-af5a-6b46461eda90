import { debounce } from '@/utils';

/**
 * 按钮防抖自定义指令
 * */
export default {
	mounted(el, binding) {
		const { value, arg } = binding;
		const clickHandler = debounce(
			() => {
				el.setAttribute('aria-disabled', true);
				el.classList.add('is-disabled'); // 开始防抖时禁用按钮
				if (value && typeof value === 'function') {
					value();
				}
			},
			arg || 1000,
			() => {
				el.setAttribute('aria-disabled', false);
				el.classList.remove('is-disabled'); // 开始防抖时禁用按钮
			}
		);

		el.addEventListener('click', clickHandler);
	},
	unmounted(el, binding) {
		el.removeEventListener('click', binding.value);
	},
};
