<template>
	<el-time-picker
		v-model="data.value"
		range-separator="~"
		value-format="YYYY-MM-DD"
		start-placeholder="开始时间"
		:is-range="isRange"
		end-placeholder="结束时间"
	/>
</template>

<script setup>
import { computed, reactive } from 'vue';

const props = defineProps({
	modelValue: {
		type: String,
		default: '',
	},
	startTime: {
		type: String,
		default: '',
	},
	endTime: {
		type: String,
		default: '',
	},
	isRange: {
		type: Boolean,
		default: false,
	},
	placeholder: {
		type: String,
		default: '请选择',
	},
});

const emits = defineEmits(['update:modelValue', 'update:startTime', 'update:endTime']);

const data = reactive({
	value: computed({
		get: () => {
			if (props.isRange) {
				return [props.startTime, props.endTime];
			} else {
				return props.modelValue;
			}
		},
		set: (val) => {
			if (props.isRange) {
				Array.isArray(val) ? emits('update:startTime', val[0]) : emits('update:startTime', '');
				Array.isArray(val) ? emits('update:endTime', val[1]) : emits('update:endTime', '');
			} else {
				emits('update:modelValue', val);
			}
		},
	}),
});
</script>
