<template>
	<div style="width: 100%; display: flex; flex-wrap: wrap;">
		<el-upload
			v-model:file-list="state.fileList"
			:show-file-list="false"
			:limit="limit"
			:before-upload="beforeUpload"
			:on-success="uploadSuccess"
			:http-request="httpRequest"
			class="upload-box"
			ref="uploaderRef"
		>
			<template #default>
				<div class="upload-head" v-if="state.fileList.length != props.limit">
					<el-icon size="30" color="#c0ccda"><ele-plus /></el-icon>
				</div>
			</template>
			<!-- <template #file>
				<img v-for="(file,idx) in state.fileList" class="upload-head" :src="file.url" alt=""/>
			</template> -->
		</el-upload>
		<div v-for="(file,idx) in state.fileList"  style="width: 100px; height: 100px; overflow: hidden; display: flex; flex-direction: column; justify-content: center; align-items: center; margin-right: 10px;">
			<img :src="file.url" alt="" width="100px" height="100px" class="upload-head" style="position: absolute;"/>
			<el-button circle @click="delPic(file)" style="cursor: pointer; z-index:999;"><el-icon><ele-delete /></el-icon></el-button>
		</div>
		
	</div>
</template>

<script setup>
import { reactive, computed, ref } from 'vue';
import { uploadApi } from '@/api/file';

const props = defineProps({
	modelValue: {
		type: [String, Array],
		required: true,
	},
	limit: {
		type: Number,
		default: 1,
	},
	type: {
		type: String,
		default: 'image',
	},
	customStyle: {
		type: Object,
		default: () => ({}),
	},
});
const emits = defineEmits(['update:modelValue', 'change', 'del']);

const state = reactive({
	fileList: computed({
		get: () => {
			console.warn('props.modelValue: ',props.modelValue)
			return props.modelValue?props.modelValue.map(item=>({url: item})):[];
		},
		set: (val) => {
			// console.log(val)
			// emits('update:modelValue', val.map(i=>i.url));
		},
	}),
});

// 上传之前
const beforeUpload = (rawFile) => {
	if(props.modelValue && props.modelValue.length == props.limit) return false;
	return true;
};

// 自定义上传
const httpRequest = (options) => {
	return uploadApi({
		file: options.file,
		type: 'any',
	});
};

// 上传成功
const uploadSuccess = (response) => {
	emits('change', response.url)
};

const uploaderRef = ref();
const delPic = (file)=>{
	emits('del', file.url)
	uploaderRef.value.handleRemove(file);
}

</script>

<style scoped lang="scss">
.upload-head {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	width: 100px;
	height: 100px;
	border: 1px dashed #c0ccda;
	border-radius: 50%;
}
.upload-box {
	display: flex;
}
::v-deep .el-upload-list {
	display: flex;
}
</style>
