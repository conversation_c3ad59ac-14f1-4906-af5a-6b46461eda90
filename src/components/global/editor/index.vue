<script setup>
import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import { computed, onBeforeUnmount, ref, shallowRef } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { uploadApi } from '@/api/file.js';
import { ElMessage } from 'element-plus';

const props = defineProps({
	mode: {
		type: String,
		default: 'default',
	},
	modelValue: {
		type: String,
		default: '',
	},
	placeholder: {
		type: String,
		default: '请输入内容...',
	},
});

const emits = defineEmits(['update:modelValue']);

const editorRef = shallowRef();

const valueHtml = computed({
	get: () => props.modelValue,
	set: (val) => {
		emits('update:modelValue', val);
	},
});

const toolbarConfig = {};
const editorConfig = {
	placeholder: props.placeholder,
	MENU_CONF: {

		uploadImage: {
			async customUpload(file, insertFn) {
				if (!file.type.startsWith('image/')) {
					ElMessage.error('只能上传图片');
					return;
				}
				const formData = new FormData();
				formData.append('file', file);
				const res = await uploadApi(formData);
				insertFn(res.url, res.originalFilename);
			},
		},
		uploadVideo: {
			async customUpload(file, insertFn) {
				if (!file.type.startsWith('video/')) {
					ElMessage.error('只能上传视频');
					return;
				}
				const formData = new FormData();
				formData.append('file', file);
				const res = await uploadApi(formData);
				insertFn(res.url);
			},
		},
	},
};

const handleCreated = (editor) => {
	editorRef.value = Object.seal(editor);
  editorRef.value.getConfig().MENU_CONF.fontSize.fontSizeList = ['14px', '15px', '16px', '17px', '18px', '19px', '20px'];
};

onBeforeUnmount(() => {
	const editor = editorRef.value;
	if (editor == null) return;
	editor.destroy();
});
</script>

<template>
	<div style="border: 1px solid #ccc">
		<Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
		<Editor style="height: 500px; overflow-y: hidden" v-model="valueHtml" :defaultConfig="editorConfig" :mode="mode" @onCreated="handleCreated" />
	</div>
</template>

<style scoped lang="scss"></style>
