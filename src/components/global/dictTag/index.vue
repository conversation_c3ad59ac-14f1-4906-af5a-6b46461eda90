<template>
	<template v-for="item in options" :key="item.value">
		<template v-if="item.value === '' + value">
			<template v-if="defaultColor.includes(item.color)">
				<el-tag :type="item.color">{{ item.label }}</el-tag>
			</template>
			<template v-else>
				<el-tag :color="item.color">{{ item.label }}</el-tag>
			</template>
		</template>
	</template>
</template>

<script setup>
const defaultColor = ['info', 'success', 'warning', 'danger', 'primary'];
defineProps({
	options: {
		type: Array,
		default: () => [],
	},
	value: {
		type: [String, Number, Boolean],
		default: '',
	},
});
</script>

<style scoped lang="scss"></style>
