/**
 * 时间问候语
 * @param param 当前时间，new Date() 格式
 * @description param 调用 `formatAxis(new Date())` 输出 `上午好`
 * @returns string
 */
export function formatGreet(param) {
	let hour = new Date(param).getHours();
	if (hour > 22) return '夜里好';
	else if (hour > 19) return '晚上好';
	else if (hour > 17) return '傍晚好';
	else if (hour > 14) return '下午好';
	else if (hour > 12) return '中午好';
	else if (hour > 9) return '上午好';
	else if (hour > 7) return '早上好';
	else if (hour > 5) return '凌晨好';
	else return '夜里好';
}

/**
 * 计算table序号
 * @param res
 * @param pagination
 */
export function calcTableIndex(res, pagination) {
	return (res.list || []).map((p, i) => {
		p._tableIndex = 1 + i + (pagination.pageNum - 1) * pagination.pageSize;
		return p;
	});
}

/**
 * 防抖
 * @param {Function} func 回调函数
 * @param {number} wait 延迟执行毫秒数
 * @param {Function} immediate 立即执行回调函数，定时器结束后执行的函数，不传就不立即执行
 */
export function debounce(func, wait, immediate) {
	let timeout;
	return function (...args) {
		const context = this;
		if (timeout) clearTimeout(timeout);
		if (immediate) {
			const callNow = !timeout;
			timeout = setTimeout(() => {
				timeout = null;
				if (typeof immediate === 'function') {
					immediate();
				}
			}, wait);
			if (callNow) func.apply(context, args);
		} else {
			timeout = setTimeout(() => {
				func.apply(context, args);
			}, wait);
		}
	};
}



