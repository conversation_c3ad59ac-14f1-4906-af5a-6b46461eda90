import { defineStore } from 'pinia';
import { loginApi, getLoginUserInfoApi, logoutApi } from '@/api/system';
import { setToken, removeToken } from '@/utils/token';
import { formatGreet } from '@/utils';
import { ElNotification } from 'element-plus';
import { router } from '@/router';
import { store } from '@/store';
import { useMenuStore } from '@/store/modules/menu.js';

export const useUserStore = defineStore({
	id: 'userInfo',
	state: () => ({
		userInfo: {},
	}),
	persist: {
		enabled: true,
		strategies: [
			{
				storage: localStorage,
				key: 'userInfo',
				paths: ['userInfo'],
			},
		],
	},
	actions: {
		/**
		 * @description:登录
		 * */
		async login(data) {
			return new Promise((resolve, reject) => {
				loginApi(data)
					.then((res) => {
						if (res) {
							setToken(res.token, 1000 * 60 * 60 * 2);
							this.getLoginUserInfo().then((userRes) => {
								ElNotification({
									title: '登录成功',
									message: `${formatGreet(new Date())}，${userRes.nickname}`,
									type: 'success',
								});
								resolve(res);
							});
						}
					})
					.catch((res) => {
						ElNotification({
							title: '登录失败',
							message: res.data ? res.data.msg : '',
							type: 'error',
						});
						reject(res);
					});
			});
		},
		/**
		 * @description:获取登录用户信息
		 * */
		async getLoginUserInfo() {
			return new Promise((resolve) => {
				getLoginUserInfoApi().then((res) => {
					this.userInfo = res;
					resolve(res);
				});
			});
		},

		// 清除登录修改信息
		async clear() {
			removeToken();
			this.$reset();
			useMenuStore().$reset();
			localStorage.removeItem('userinfo');
			localStorage.removeItem('tabsList');
		},

		/**
		 * @description:退出登录
		 * */
		async logout() {
			logoutApi()
				.then(() => {
					this.clear();
					router.push('/login').then(() => {
						// 重新刷新页面
						location.reload();
					});
				})
				.catch(() => {
					this.clear();
					router.push('/login').then(() => {
						// 重新刷新页面
						location.reload();
					});
				});
		},
	},
});
export function useUserStoreHook() {
	return useUserStore(store);
}
