import { defineStore } from 'pinia';

export const useDictStore = defineStore({
	id: 'dict',
	state: () => ({
		dict: [],
	}),
	actions: {
		// 设置字典
		setDict(key, value) {
			if (key) {
				this.dict.push({
					key,
					value,
				});
			}
		},
		// 获取字典
		getDict(key) {
			let result = this.dict.find((item) => item.key === key);
			if (result) {
				return result.value;
			} else {
				return '';
			}
		},
	},
});
