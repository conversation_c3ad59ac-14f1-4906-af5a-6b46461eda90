/**
* @description 修改element-ui组件默认样式
*/
:root{
  --el-color-primary: #2e5cf6;
  --el-color-primary-dark-2: #2e5cf6;
}
.el-button--primary {
  --el-button-hover-bg-color: #2952dd;
  --el-button-hover-border-color: #2952dd;
}

.el-button--primary.is-link {
  --el-button-text-color: #333;
  --el-button-hover-link-text-color: #2e5cf6;
}

.el-button--primary.is-plain {
  --el-button-text-color: #2e5cf6;
  --el-button-border-color: #2e5cf6;
  --el-button-bg-color: rgba(46, 92, 246, 0.3);
}
//--------------------------------------------------------------菜单
.el-menu {
  border: none !important;
  .el-menu-item {
    &:hover {
      background-color: #F2F3F5 !important;
    }
    .menu-txt{
      z-index: 1;
    }
    &.is-active {
      color: #2e5cf6;
      background: #F2F3F5 !important;
    }
  }

  .el-sub-menu__title:hover {
    background-color: #F2F3F5 !important;
  }

  // .el-sub-menu {
  //   &.is-active {
  //     .el-sub-menu__title {
  //       color: #ef5945 !important;
  //     }
  //   }
  // }
}
//--------------------------------------------------------------卡片
.el-card{
  --el-card-border-color: var(--border-color-light);
}

//--------------------------------------------------------------布局容器
.el-main{
  --el-main-padding:10px;
}
//--------------------------------------------------------------表单
.el-input{
  width: 100% !important;
}

.el-input-number{
  width: 100% !important;
  .el-input__inner{
    text-align: left;
  }
}

.el-dialog{
  .el-dialog__body{
    max-height: calc(100vh - 220px);
    overflow-x: hidden;
    overflow-y: auto;
  }
}

//滚动条
.el-scrollbar{
  width: 100%;
}

.el-menu-item, .el-sub-menu__title {
  color: #3C3D3F;
}

/* 面包屑*/
.el-breadcrumb {
  .el-breadcrumb__inner.is-link {
    color: #3C3D3F;
    font-weight: unset;
  }
  .el-breadcrumb__item:last-child .el-breadcrumb__inner{
    color: #3C3D3F;
    opacity: .7;
  }
}

/* 表格*/
.el-table{
  width: 100%;
  color: #3C3D3F !important;
}
.el-table thead{
  color: #3C3D3F !important;
}
.el-table th.el-table__cell{
  font-size: 14px;
  background-color: #F2F3F5 !important;
  color: #1D2129 !important;
  font-weight: 400;
}
.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  // border-bottom: 1px solid
}

.el-form-item__label{
  color: #3C3D3F !important;
}

.el-input__inner{
  color: #3C3D3F !important;
}

.el-select-dropdown__item{
  color: #3C3D3F !important;
}

.el-dropdown{
  color: #3C3D3F !important;
}

.el-button{
  font-weight: 400;
}

.el-table__cell .el-button + .el-button{
  margin-left: 5px !important;
}

.el-dialog {
  --el-dialog-margin-top: 0px !important;
  --el-dialog-margin-bottom: 0px !important;
  margin: 0 auto !important;
}

.el-overlay-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

.el-input__wrapper {
  background: #F2F3F5;
  border-radius: 0px;
  box-shadow: none;
}

.el-date-editor.el-input__wrapper {
  border-radius: 0px;
  box-shadow: none;
}

.el-input-group__append {
  box-shadow: none;
  background-color: #F2F3F5;
  border-radius: 0px;
  border-left: 1px solid #ddd;
  color: #4E5969;
}

.el-select__wrapper {
  background: #F2F3F5;
  border-radius: 0px;
  box-shadow: none;
}

.el-button {
  border-radius: 2px;
}

.el-textarea__inner {
  background: #F2F3F5;
  border-radius: 0px;
  box-shadow: none;
}
