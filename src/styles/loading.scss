.loading-box {
	display: flex;
	justify-content: center;
	align-items: center;
	min-height: 100vh;
}
.loader {
	position: relative;
	width: 160px;
	height: 160px;
	animation: animate 2s linear infinite;
}
.loader span {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
}
/* 定位 伪元素，设置流水的珠子 */
.loader span::before {
	content: '';
	position: absolute;
	top: 0;
	width: 30px;
	height: 30px;
	/* 珠子颜色设置 */
	background: linear-gradient(45deg, var(--el-color-primary), var(--el-color-primary-light-3));
	border-radius: 50%;
	/* 设置阴影 */
	box-shadow: 0 0 3px var(--el-color-primary-light-1);
}
.loader span{
	/* 设置动画旋转 */
	transform: rotate(calc(45deg*var(--i)));
}

.loader span::before {
	left: calc(50% - 20px);
}
/* 这里设置了旋转动画 */
@keyframes animate {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
.rotate {
	animation: animate 4s ease-in-out infinite;
	/* 设置延迟 */
	animation-delay: calc(-0.2s*var(--j));
}
.loader {
	filter: url(#fluid);
	/* 去掉临时背景颜色为透明 */
	background: transparent;
}
svg {
	width: 0;
	height: 0;
}

.word {
	position: absolute;
	color: #2e5cf6;
	font-size: 1.2em;
	animation: words 3s linear infinite;
}
/* 这里设置了文字的缩放动画 */
@keyframes words {
	0% {
		transform: scale(1.2);
	}

	25% {
		transform: scale(1);
	}

	50% {
		transform: scale(0.8);
	}

	75% {
		transform: scale(1);
	}

	100% {
		transform: scale(1.2);
	}
}
