/* -----------------------------------------初始化样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none !important;
  list-style: none;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  font-family: Helvetica Neue,PingFang SC,Hiragino Sans GB,Microsoft YaHei,SimSun,sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  //-webkit-tap-highlight-color: transparent;
  font-size: 14px;
  overflow: hidden;
  position: relative;
}


/* -----------------------------------------字体大小全局样式 */
@for $i from 10 through 32 {
  .font#{$i} {
    font-size: #{$i}px !important;
  }
}

/* -----------------------------------------颜色值 */
.color-primary {
  color: var(--el-color-primary);
}
.color-success {
  color: var(--el-color-success);
}
.color-warning {
  color: var(--el-color-warning);
}
.color-danger {
  color: var(--el-color-danger);
}
.color-info {
  color: var(--el-color-info);
}

/* -----------------------------------------滚动条 */
::-webkit-scrollbar-track-piece {
  background-color: transparent;
}
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background-color: transparent;
}
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: hsla(220, 4%, 58%, .3);
}

.el-image-viewer__wrapper{
  //top:20%;
  //bottom:20%;
  //left:20%;
  //right:20%;
}
.el-image-viewer__wrapper .el-image-viewer__canvas img{
  width: 36%;
}
