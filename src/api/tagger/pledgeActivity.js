import request from '@/utils/request';

// 添加质押活动
export function addPledgeActivityApi(data) {
	return request.post({
		url: '/admin/pledgeActivity/addPledgeActivity',
		data,
	});
}

// 修改质押活动
export function updatePledgeActivityApi(data) {
	return request.post({
		url: '/admin/pledgeActivity/updatePledgeActivity',
		data,
	});
}

// 删除质押活动
export function deletePledgeActivityApi(id) {
	return request.post({
		url: '/admin/pledgeActivity/deletePledgeActivity/' + id,
	});
}

// 获取质押活动详情
export function getPledgeActivityApi(id) {
	return request.post({
		url: '/admin/pledgeActivity/getPledgeActivity/' + id,
	});
}

// 获取质押活动分页列表
export function getPledgeActivityPageApi(data) {
	return request.post({
		url: '/admin/pledgeActivity/getPledgeActivityPage',
		data,
	});
}

export function getPledgeActivityStatisticsApi() {
	return request.post({
		url: '/admin/pledgeActivity/getPledgeActivityStatistics'
	})
}

// 获取当前进行中的活动审核者数量
export function getLastAuditorNumApi() {
	return request.post({
		url: '/admin/pledgeActivity/getLastAuditorNum'
	});
}

// 根据审核者数量获取统计数据
export function getPledgeActivityDataInfoApi(auditorNum) {
	return request.post({
		url: `/admin/pledgeActivity/getPledgeActivityDataInfo/${auditorNum}`
	});
}

// 获取分页列表
export function getPledgeActivityDataPageApi(data) {
	return request.post({
		url: '/admin/pledgeActivity/getPledgeActivityDataPage',
		data,
	});
}