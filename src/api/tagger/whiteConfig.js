import request from '@/utils/request';

// 获取标注白名单配置分页列表
export function getWhiteTagConfigPageApi(data) {
	return request.post({
		url: '/admin/whiteTagConfig/getWhiteTagConfigPage',
		data,
	});
}

// 获取标注白名单配置详情
export function getWhiteTagConfigApi(id) {
	return request.post({
		url: '/admin/whiteTagConfig/getWhiteTagConfig/' + id,
	});
}

// 删除标注白名单配置
export function deleteWhiteTagConfigApi(data) {
	return request.post({
		url: '/admin/whiteTagConfig/deleteWhiteTagConfig/',
		data,
	});
}

// 导入标注白名单配置
export function importWhiteTagConfigPageApi(data) {
	return request.post({
		url: '/admin/whiteTagConfig/importWhiteTagConfigPage',
		data,
	});
}

// 获取审核白名单配置分页列表
export function getWhiteAuditConfigPageApi(data) {
	return request.post({
		url: '/admin/whiteAuditConfig/getWhiteAuditConfigPage',
		data,
	});
}

// 获取审核白名单配置详情
export function getWhiteAuditConfigApi(id) {
	return request.post({
		url: '/admin/whiteAuditConfig/getWhiteAuditConfig/' + id,
	});
}

// 删除标注白名单配置
export function deleteWhiteAuditConfigApi(data) {
	return request.post({
		url: '/admin/whiteAuditConfig/deleteWhiteTagConfig/',
		data,
	});
}

// 导入标注白名单配置
export function importWhiteAuditConfigPageApi(data) {
	return request.post({
		url: '/admin/whiteAuditConfig/importWhiteTagConfigPage',
		data,
	});
}

// 修改审核白名单自动审核配置
export function updateWhiteAutoAuditConfigApi(data) {
	return request.post({
		url: '/admin/whiteAutoAuditConfig/updateWhiteAutoAuditConfig',
		data,
	});
}

// 获取审核白名单自动审核配置详情
export function getWhiteAutoAuditConfigApi(id) {
	return request.post({
		url: '/admin/whiteAutoAuditConfig/getWhiteAutoAuditConfig/' + id,
	});
}
