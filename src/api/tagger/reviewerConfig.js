import request from '@/utils/request';

// 添加审核人员配置
export function addReviewerConfig(data) {
	return request.post({
		url: '/admin/reviewerConfig/addReviewerConfig',
		data,
	});
}

// 修改审核人员配置
export function updateReviewerConfig(data) {
	return request.post({
		url: '/admin/reviewerConfig/updateReviewerConfig',
		data,
	});
}

// 删除审核人员配置
export function deleteReviewerConfig(id) {
	return request.post({
		url: '/admin/reviewerConfig/deleteReviewerConfig/' + id,
	});
}

// 获取审核人员配置详情
export function getReviewerConfig(id) {
	return request.post({
		url: '/admin/reviewerConfig/getReviewerConfig/' + id,
	});
}

// 获取审核人员配置分页列表
export function getReviewerConfigPage(data) {
	return request.post({
		url: '/admin/reviewerConfig/getReviewerConfigPage',
		data,
	});
}

export function importReviewerConfigApi(type, data) {
	return request.post({
		url: '/admin/reviewerConfig/importReviewerConfig/' + type,
		data,
	});
}

export function confirmImportReviewerConfigApi(key) {
	return request.post({
		url: '/admin/reviewerConfig/configImportReviewerConfig/' + key,
	});
}

export function batchDeleteReviewerConfigApi(ids) {
	return request.post({
		url: '/admin/reviewerConfig/batchDeleteReviewerConfig',
		data: {
			ids,
		},
	});
}
