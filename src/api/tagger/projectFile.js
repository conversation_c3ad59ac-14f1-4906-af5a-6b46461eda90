import request from '@/utils/request';

// 添加项目文件
export function addProjectFile(data) {
	return request.post({
		url: '/admin/projectFile/addProjectFile',
		data,
	});
}

// 修改项目文件
export function updateProjectFile(data) {
	return request.post({
		url: '/admin/projectFile/updateProjectFile',
		data,
	});
}

// 删除项目文件
export function deleteProjectFile(id) {
	return request.post({
		url: '/admin/projectFile/deleteProjectFile/' + id,
	});
}

// 获取项目文件详情
export function getProjectFile(id) {
	return request.post({
		url: '/admin/projectFile/getProjectFile/' + id,
	});
}

// 获取项目文件分页列表
export function getProjectFilePage(data) {
	return request.post({
		url: '/admin/projectFile/getProjectFilePage',
		data,
	});
}

