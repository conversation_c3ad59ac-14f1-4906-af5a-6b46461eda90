import request from '@/utils/request';

export function confirmManualProjectDataApi(data) {
	return request.post({
		url: '/admin/projectHMConfirm',
		data,
		axiosConfig: {
			baseURL: import.meta.env.VITE_DM_BASE_URL,
		},
	});
}

export function confirmAIProjectDataApi(data) {
	return request.post({
		url: '/admin/projectAIConfirm',
		data,
		axiosConfig: {
			baseURL: import.meta.env.VITE_DM_BASE_URL,
		},
	});
}
