import request from '@/utils/request';

// 添加质押活动用户
export function addPledgeActivityUserApi(data) {
	return request.post({
		url: '/admin/pledgeActivityUser/addPledgeActivityUser',
		data,
	});
}

// 修改质押活动用户
export function updatePledgeActivityUserApi(data) {
	return request.post({
		url: '/admin/pledgeActivityUser/updatePledgeActivityUser',
		data,
	});
}

// 删除质押活动用户
export function deletePledgeActivityUserApi(id) {
	return request.post({
		url: '/admin/pledgeActivityUser/deletePledgeActivityUser/' + id,
	});
}

// 获取质押活动用户详情
export function getPledgeActivityUserApi(id) {
	return request.post({
		url: '/admin/pledgeActivityUser/getPledgeActivityUser/' + id,
	});
}

// 获取质押活动用户分页列表
export function getPledgeActivityUserPageApi(data) {
	return request.post({
		url: '/admin/pledgeActivity/getPledgeActivityUserPage',
		data,
	});
}

export function downloadPledgeActivityUserPageApi(pledgeActivityId) {
	return request.get({
		url: `/admin/pledgeActivity/downloadPledgeActivityUserList/${pledgeActivityId}`,
		responseType: 'blob'
	});
}