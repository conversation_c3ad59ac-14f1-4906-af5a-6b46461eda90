import request from '@/utils/request';

// 添加项目
export function addProjectApi(data) {
	return request.post({
		url: '/admin/project/addProject',
		data,
	});
}

// 修改项目
export function updateProjectApi(data) {
	return request.post({
		url: '/admin/project/updateProject',
		data,
	});
}

// 删除项目
export function deleteProjectApi(id) {
	return request.post({
		url: '/admin/project/deleteProject/' + id,
	});
}

// 获取项目详情
export function getProjectApi(id) {
	return request.post({
		url: '/admin/project/getProject/' + id,
	});
}

// 获取项目分页列表
export function getProjectPageApi(data) {
	return request.post({
		url: '/admin/project/getProjectPage',
		data,
	});
}

// 获取最新的NFT钱包密钥
export function getLastMintNFTApi() {
	return request.post({
		url: '/admin/project/getLastMintNFT',
	});
}

// 提交 mint NFT
export function mintNF<PERSON>pi(data) {
	return request.post({
		url: '/admin/project/mintNFT',
		data,
	});
}

// 上传greenfield数据
export const uploadOriginalToGreenfieldApi = (projectId) => {
	return request.post({
		url: `/admin/project/uploadOriginalToGf/{${projectId}`,
	});
};

export const uploadTagToGreenfieldApi = (projectId) => {
	return request.post({
		url: `/admin/project/uploadTagToGf/${projectId}`,
	});
};

export const getProjectUserListApi = (data) => {
	return request.post({
		url: `/admin/project/getProjectUserPage`,
		data,
	});
}

export const updateProjectStatusApi = (data) => {
	return request.post({
		url: `/admin/project/updateProjectStatus`,
		data,
	});
}