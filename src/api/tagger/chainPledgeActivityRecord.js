import request from '@/utils/request';

// 获取质押活动链上记录分页列表
export function getChainPledgeActivityRecordPage(data) {
	return request.post({
		url: '/admin/chainPledgeActivityRecord/getChainPledgeActivityRecordPage',
		data,
	});
}

// 下载质押活动链上记录数据
export function getDownloadUrl(type, token) {
	return window.origin + `/api/admin/chainPledgeActivityRecord/downloadChainPledgeActivityRecord/${type}?Authorization=${token}`;
} 