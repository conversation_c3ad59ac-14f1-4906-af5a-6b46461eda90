import request from '@/utils/request';

// 添加质押用户链上记录
export function addPledgeUserChainRecord(data) {
	return request.post({
		url: '/admin/pledgeUserChainRecord/addPledgeUserChainRecord',
		data,
	});
}

// 修改质押用户链上记录
export function updatePledgeUserChainRecord(data) {
	return request.post({
		url: '/admin/pledgeUserChainRecord/updatePledgeUserChainRecord',
		data,
	});
}

// 删除质押用户链上记录
export function deletePledgeUserChainRecord(id) {
	return request.post({
		url: '/admin/pledgeUserChainRecord/deletePledgeUserChainRecord/' + id,
	});
}

// 获取质押用户链上记录详情
export function getPledgeUserChainRecord(id) {
	return request.post({
		url: '/admin/pledgeUserChainRecord/getPledgeUserChainRecord/' + id,
	});
}

// 获取质押用户链上记录分页列表
export function getPledgeUserChainRecordPage(data) {
	return request.post({
		url: '/admin/pledgeUserChainRecord/getPledgeUserChainRecordPage',
		data,
	});
}

