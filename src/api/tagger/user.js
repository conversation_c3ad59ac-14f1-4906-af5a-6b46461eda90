import request from '@/utils/request';

// 添加用户信息
export function addUser(data) {
	return request.post({
		url: '/admin/user/addUser',
		data,
	});
}

// 修改用户信息
export function updateUser(data) {
	return request.post({
		url: '/admin/user/updateUser',
		data,
	});
}

export function updateUserStatusApi(data) {
	return request.post({
		url: '/admin/user/updateUserStatus',
		data
	})
}

// 删除用户信息
export function deleteUser(id) {
	return request.post({
		url: '/admin/user/deleteUser/' + id,
	});
}

// 获取用户信息详情
export function getUser(id) {
	return request.post({
		url: '/admin/user/getUser/' + id,
	});
}

// 获取用户信息分页列表
export function getUserPage(data) {
	return request.post({
		url: '/admin/user/getUserPage',
		data,
	});
}

export function getUserStatisticsApi(data) {
	return request.post({
		url: '/admin/user/getScoreTotalTaskList',
		data,
	});
}