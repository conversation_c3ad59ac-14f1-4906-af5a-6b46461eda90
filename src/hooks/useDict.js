import { useDictStore } from '@/store/modules/dict';
import { getSysDictListByDictCodeApi } from '@/api/dict';
import { ref, toRefs } from 'vue';

/**
 * 获取字典数据
 * */
export function useDict(...args) {
	let dicts = ref({});
	const dictStore = useDictStore();
	args.forEach((key) => {
		dicts.value['dict_' + key] = [];
		let dict = dictStore.getDict('dict_' + key);
		if (dict) {
			dicts.value['dict_' + key] = dict;
		} else {
			getSysDictListByDictCodeApi(key).then((res) => {
				dicts.value['dict_' + key] = res || [];
				dictStore.setDict('dict_' + key, dicts.value['dict_' + key]);
			});
		}
	});
	return toRefs(dicts.value);
}
