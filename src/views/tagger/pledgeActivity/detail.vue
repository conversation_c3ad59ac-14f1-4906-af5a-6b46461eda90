<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close title="质押活动详情" @close="closeDialog" draggable width="900px">
			<div v-loading="formLoading">
				<!-- 基础信息 -->
				<div class="section">
					<div class="section-title">基础信息</div>
					<el-descriptions :column="2" border>
						<el-descriptions-item label="创建时间">{{ form.createTime }}</el-descriptions-item>
						<el-descriptions-item label="质押活动发布时间">{{ form.activityStartDate }}</el-descriptions-item>
						<el-descriptions-item label="审核开始时间">{{ form.activityEndDate }}</el-descriptions-item>
						<el-descriptions-item label="审核持续天数">{{ form.diffDay || '--' }}天</el-descriptions-item>
						<el-descriptions-item label="审核结束时间">{{ form.auditEndDate || '--' }}</el-descriptions-item>
						<el-descriptions-item label="最低质押tag代币数量">{{ form.minimumTagNum }}</el-descriptions-item>
						<el-descriptions-item label="设置的审核者数量">{{ form.auditorNum }}</el-descriptions-item>
						<el-descriptions-item label="实际参与审核人数">{{ form.auditUserNum }}</el-descriptions-item>
						<el-descriptions-item label="完成审核图片量">{{ form.auditTagNum }}</el-descriptions-item>
						<el-descriptions-item label="获得总积分">{{ form.activityTotalScore }}</el-descriptions-item>
					</el-descriptions>
				</div>

				<!-- 获得审核资格名单 -->
				<div class="section mt-4">
					<div class="section-title">获得审核资格名单</div>
					<el-table :data="userList" border v-loading="userListLoading">
						<el-table-column prop="top" label="排名" width="80" align="center" />
						<el-table-column prop="walletAddress" label="钱包地址" align="center" />
						<el-table-column label="是否已方审核人员" align="center">
							<template #default="{ row }">
								{{ row.selfStatus ? '是' : '否' }}
							</template>
						</el-table-column>
						<el-table-column prop="historyTotalScore" label="快照时质押量" align="center" />
						<el-table-column prop="realFinishAuditNum" label="实际完成的审核任务量" align="center" />
						<el-table-column prop="scoreAuditNum" label="得分审核量" align="center" />
						<el-table-column prop="totalScore" label="获得总积分" align="center" />
						<el-table-column label="操作" align="center" width="120">
							<template #default="{ row }">
								<el-button link type="primary" @click="viewUser(row)">查看用户</el-button>
							</template>
						</el-table-column>
					</el-table>

					<!-- 分页 -->
					<div class="pagination-container">
						<el-pagination :current-page="queryParams.pageNum" :page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
					</div>

					<div class="mt-3">
						<el-button type="primary" @click="downloadList">下载资格名单</el-button>
					</div>
				</div>
			</div>
		</el-dialog>

		<!-- 用户详情组件 -->
		<UserDetail ref="userDetailRef" />
	</div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getPledgeActivityApi } from '@/api/tagger/pledgeActivity';
import { getPledgeActivityUserPageApi, downloadPledgeActivityUserPageApi } from '@/api/tagger/pledgeActivityUser';
import { reactive, ref } from 'vue';
import { getToken } from '@/utils/token.js';
import UserDetail from '@/views/tagger/user/user-detail.vue';

const emits = defineEmits(['refresh']);

// 表单数据
const form = ref({});
const formLoading = ref(false);

// 用户列表数据
const userList = ref([]);
const userListLoading = ref(false);
const total = ref(0);

// 查询参数
const queryParams = reactive({
	pledgeActivityId: 0,
	pageNum: 1,
	pageSize: 10,
	keyword: '',
	orderByColumn: '',
	orderByAsc: true,
});

// 弹框数据
const dialogData = reactive({
	isShow: false,
	id: null,
});

// 用户详情组件引用
const userDetailRef = ref(null);

// 打开弹框
const openDialog = async (row) => {
	if (!row?.id) return;
	dialogData.isShow = true;
	dialogData.id = row.id;
	queryParams.pledgeActivityId = row.id;
	await getDetails();
	getUserList();
};

// 获取详情
const getDetails = async () => {
	formLoading.value = true;
	try {
		const res = await getPledgeActivityApi(dialogData.id);
		form.value = res;
	} finally {
		formLoading.value = false;
	}
};

// 获取用户列表
const getUserList = () => {
	userListLoading.value = true;
	getPledgeActivityUserPageApi({
		...queryParams,
		pledgeActivityId: Number(dialogData.id),
	})
		.then((res) => {
			userList.value = res.list || [];
			total.value = res.total || 0;
		})
		.catch((error) => {
			console.error('获取用户列表失败:', error);
			ElMessage.error('获取用户列表失败');
		})
		.finally(() => {
			userListLoading.value = false;
		});
};

// 分页大小变化
const handleSizeChange = (size) => {
	queryParams.pageSize = size;
	getUserList();
};

// 页码变化
const handleCurrentChange = (page) => {
	queryParams.pageNum = page;
	getUserList();
};

// 查看用户
const viewUser = (row) => {
	userDetailRef.value.openDrawer(row.userId);
};

// 下载资格名单
const downloadList = async () => {
	const url = window.origin + '/api/admin/pledgeActivity/downloadPledgeActivityUserList/' + dialogData.id + '?Authorization=' + getToken();

	window.open(url, '_blank');
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
	dialogData.id = null;
	form.value = {};
	userList.value = [];
	total.value = 0;
	queryParams.pageNum = 1;
	queryParams.pageSize = 10;
	queryParams.keyword = '';
	queryParams.pledgeActivityId = 0;
};

defineExpose({
	openDialog,
});
</script>

<style scoped>
.section {
	background-color: #f5f7fa;
	border-radius: 4px;
	padding: 16px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 16px;
	position: relative;
	padding-left: 12px;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 16px;
	background-color: #409eff;
	border-radius: 2px;
}

.mt-3 {
	margin-top: 12px;
}

.mt-4 {
	margin-top: 16px;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
