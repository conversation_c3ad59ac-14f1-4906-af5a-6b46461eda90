<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="600px">
			<el-form ref="formRef" :rules="rules" :model="form" label-width="160px" v-loading="formLoading">
				<div class="section">
					<el-form-item label="质押活动发布时间" prop="activityStartDate">
						<el-date-picker v-model="form.activityStartDate" type="datetime" placeholder="请选择活动发布时间" value-format="YYYY-MM-DD HH:mm:ss" :shortcuts="shortcuts" :disabled-date="disabledStartDate" :show-now="false" />
					</el-form-item>
					<el-form-item label="参与质押倒计时" prop="activityCountdownHour">
						<el-input-number v-model="form.activityCountdownHour" :min="1" :max="999" :precision="0" controls-position="right" placeholder="请输入倒计时小时数">
							<template #append>小时</template>
						</el-input-number>
					</el-form-item>
					<el-form-item label="本次所需审核者数量" prop="auditorNum">
						<el-input-number v-model="form.auditorNum" :min="1" :max="999999" :precision="0" controls-position="right" placeholder="请输入审核者数量" />
					</el-form-item>
					<el-form-item label="最低质押tag代币数量" prop="minimumTagNum">
						<el-input-number v-model="form.minimumTagNum" :min="0" :max="99999999" :precision="0" controls-position="right" placeholder="请输入最低质押tag代币数量" />
					</el-form-item>
				</div>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" :loading="formSubmitting" @click="submit">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { dayjs, ElMessage } from 'element-plus';
import { addPledgeActivityApi, updatePledgeActivityApi } from '@/api/tagger/pledgeActivity';
import { reactive, ref } from 'vue';
import { cloneDeep } from 'lodash-es';

const formRef = ref();
const emits = defineEmits(['refresh']);

// 表单默认值
const defaultForm = {
	activityStartDate: '',
	countDown: null,
	auditorNum: null,
	minimumTagNum: 0,
};

const shortcuts = [
	{
		text: '此刻',
		value: () => {
			const date = new Date();
			// 获取本地时间与UTC时间的时差（分钟）
			const timezoneOffset = date.getTimezoneOffset();
			// 减去时差，转换为UTC时间
			date.setMinutes(date.getMinutes() + timezoneOffset);
			return date;
		},
	},
];

// 表单数据
const form = ref(cloneDeep(defaultForm));
const formLoading = ref(false);
const formSubmitting = ref(false);

// 验证规则
const rules = {
	activityStartDate: [
		{ required: true, message: '请选择活动发布时间', trigger: 'change' },
		{
			validator: (rule, value, callback) => {
				if (!value) {
					callback();
					return;
				}
				// 将选择的时间转换为UTC时间	
				const date = new Date(value);
				const timezoneOffset = date.getTimezoneOffset();
				const selectedTime = date.setMinutes(date.getMinutes() - timezoneOffset)
				const now = new Date()

				if (selectedTime < now) {
					callback(new Error('活动发布时间不能是UTC过去时间'));
				} else {
					callback();
				}
			},
			trigger: 'change',
		},
	],
	activityCountdownHour: [{ required: true, message: '请输入倒计时小时数', trigger: 'blur' }],
	auditorNum: [{ required: true, message: '请输入审核者数量', trigger: 'blur' }],
	minimumTagNum: [{ required: true, message: '请输入审核者数量', trigger: 'blur' }],
};

// 禁用过去的日期
const disabledStartDate = (time) => {
	const date = new Date();
	// 获取本地时间与UTC时间的时差（分钟）
	const timezoneOffset = date.getTimezoneOffset();
	// 减去时差，转换为UTC时间
	date.setMinutes(date.getMinutes() + timezoneOffset);
	return time.getTime() < date.getTime() - 8.64e7; // 禁用今天之前的日期
};

// 弹框数据
const dialogData = reactive({
	isShow: false,
	title: '新建质押活动',
	id: null,
});

// 打开弹框
const openDialog = async (row) => {
	dialogData.isShow = true;
	if (row?.id) {
		dialogData.id = row.id;
		dialogData.title = '编辑质押活动';
		form.value = cloneDeep(row);
	} else {
		form.value = cloneDeep(defaultForm);
	}
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
	dialogData.id = null;
	formRef.value?.resetFields();
};

// 提交
const submit = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid) => {
		if (valid) {
			formSubmitting.value = true;
			const promise = dialogData.id ? updatePledgeActivityApi(form.value) : addPledgeActivityApi(form.value);
			promise
				.then(() => {
					ElMessage.success('操作成功');
					closeDialog();
					emits('refresh');
				})
				.finally(() => {
					formSubmitting.value = false;
				});
		}
	});
};

defineExpose({
	openDialog,
});
</script>

<style scoped>
.section {
	padding: 20px;
	border-radius: 4px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 20px;
}

:deep(.el-input-number .el-input__wrapper) {
	padding-right: 0;
}
</style>
