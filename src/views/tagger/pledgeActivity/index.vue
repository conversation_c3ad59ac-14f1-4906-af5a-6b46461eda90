<template>
	<el-card shadow="never" class="card-box">
		<!-- 统计信息 -->
		<div class="statistics-row" v-loading="statisticsLoading">
			<p class="statistics-text">最近一次审核开启时间: {{ statistics.lastAuditStartTime || '--' }}</p>
			<p class="statistics-text">当前手工标注任务未取审核的总量: {{ statistics.tagNum || 0 }}，对应审核任务量: {{ statistics.auditStartNum || 0 }}~{{ statistics.auditEndNum || 400 }}</p>
		</div>

		<el-form :model="queryForm" label-width="120px">
			<el-row :gutter="20">
				<el-col :lg="8" :md="12" :sm="24" :xl="6">
					<el-form-item label="活动发布时间">
						<date-picker v-model:startDate="queryForm.activityStartDateStart" v-model:endDate="queryForm.activityStartDateEnd" type="daterange" clearable start-placeholder="开始时间" end-placeholder="结束时间" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label-width="0">
						<el-button type="primary" @click="onSearch">
							<el-icon>
								<ele-search />
							</el-icon>
							<span class="search-btn__left">查询</span>
						</el-button>
						<el-button @click="onReset">
							<el-icon>
								<ele-refresh />
							</el-icon>
							<span class="search-btn__left">重置</span>
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div v-auth="'t:pledge:activity:add'" class="table-btn-box mb-3">
			<el-button type="primary" :disabled="!addingAvailable" @click="openDialog">
				<el-icon class="mr-1">
					<ele-circle-plus />
				</el-icon>
				新 增
			</el-button>
		</div>
		<el-table v-loading="tableData.isLoading" :data="tableData.data" border>
			<el-table-column type="index" label="序号" width="60" align="center" />
			<el-table-column prop="createTime" label="创建时间" align="center" min-width="150" />
			<el-table-column prop="activityStartDate" label="活动发布时间（活动开启时间）" align="center" min-width="180"> </el-table-column>
			<el-table-column label="倒计时" align="center" width="120">
				<template #default="{ row }">
					{{ getCountDown(row) }}
				</template>
			</el-table-column>
			<el-table-column prop="activityEndDate" label="审核开始时间（活动结束时间）" align="center" min-width="180"> </el-table-column>
			<el-table-column label="审核持续天数" align="center" width="120">
				<template #default="{ row }"> {{ row.diffDay || '--' }}天 </template>
			</el-table-column>
			<el-table-column prop="auditEndDate" label="审核结束时间" align="center" min-width="150" :formatter="(row) => row.auditEndDate || '--'"> </el-table-column>
			<el-table-column prop="minimumTagNum" label="最低质押tag代币数量" align="center" width="100" />
			<el-table-column prop="auditorNum" label="审核者数量" align="center" width="100" />
			<el-table-column prop="auditUserNum" label="实际参与审核人数" align="center" width="140" />
			<el-table-column prop="auditTagNum" label="完成审核图片量" align="center" width="140" />
			<el-table-column prop="activityTotalScore" label="获得总积分" align="center" width="100" />
			<el-table-column label="操作" fixed="right" align="center" width="120">
				<template #default="{ row }">
					<el-button link type="primary" @click="openDetailDialog(row)">查看</el-button>
					<el-button v-if="row.deletable" link type="danger" @click="delTable(row)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>

		<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

		<TableForm ref="tableDialogRef" @refresh="getTableList" />
		<DetailDialog ref="detailDialogRef" @refresh="getTableList" />
	</el-card>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { deletePledgeActivityApi, getPledgeActivityPageApi, getPledgeActivityStatisticsApi } from '@/api/tagger/pledgeActivity';
import TableForm from './form.vue';
import DetailDialog from './detail.vue';
import { reactive, ref, onMounted, onBeforeUnmount, computed } from 'vue';
import moment from 'moment';

let queryForm = ref({});
const statistics = ref({});
const statisticsLoading = ref(false);

// 获取统计数据
const getStatistics = () => {
	statisticsLoading.value = true;
	getPledgeActivityStatisticsApi().then(res => {
		statistics.value = res || {};
		statisticsLoading.value = false;
	}).catch(error => {
		console.error('获取统计数据失败:', error);
		statisticsLoading.value = false;
	});
};

// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {};
	pageData.pageNum = 1;
	getTableList();
};

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

// 排序
const orderBy = ref({});
const sortChange = ({ prop, order }) => {
	if (order) {
		orderBy.value.orderByColumn = prop;
		orderBy.value.orderByAsc = order === 'ascending';
	} else {
		orderBy.value = {};
	}
	pageData.pageNum = 1;
	getTableList();
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 倒计时定时器
let timer = null;

const addingAvailable = computed(() => {
	return tableData.data.every((item) => {
		return moment(item.activityEndDate).isBefore(moment());
	});
});

// 计算倒计时
const getCountDown = (row) => {
	if (!row.activityStartDate || !row.activityEndDate) return '--';

	const now = new Date().getTime();
	const startTime = new Date(row.activityStartDate + 'Z').getTime();
	const endTime = new Date(row.activityEndDate + 'Z').getTime();

	let diffTime;

	// 如果活动还未开始或已结束，显示总时长
	if (now < startTime || now > endTime) {
		// diffTime = endTime - startTime;
		return '--'
	} else {
		// 活动进行中，显示到结束的倒计时
		diffTime = endTime - now;
	}

	// 转换为小时:分钟:秒格式
	const hours = Math.floor(diffTime / (1000 * 60 * 60));
	const minutes = Math.floor((diffTime % (1000 * 60 * 60)) / (1000 * 60));
	const seconds = Math.floor((diffTime % (1000 * 60)) / 1000);

	return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 更新倒计时
const updateCountDown = () => {
	if (tableData.data.length) {
		// 强制更新表格数据以刷新倒计时显示
		tableData.data = [...tableData.data];
	}
};

// 组件挂载时启动定时器
onMounted(() => {
	// 每秒更新一次倒计时
	timer = setInterval(updateCountDown, 1000);
	// 获取统计数据
	getStatistics();
});

// 组件卸载前清除定时器
onBeforeUnmount(() => {
	if (timer) {
		clearInterval(timer);
		timer = null;
	}
});

// 获取表格列表
const getTableList = () => {
	tableData.isLoading = true;
	getPledgeActivityPageApi({ ...pageData, ...queryForm.value, ...orderBy.value }).then((res) => {
		tableData.data = res?.list?.map((p, i) => {
			p.number = 1 + i + (pageData.pageNum - 1) * pageData.pageSize;
			p.deletable = moment(p.activityStartDate + 'Z').isAfter(moment());
			return p;
		});
		pageData.total = res?.total;
		tableData.isLoading = false;
	});
};

// 删除列表数据
function delTable(row) {
	ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deletePledgeActivityApi(row.id).then(() => {
				ElMessage.success('删除成功');
				getTableList();
			});
		})
		.catch(() => {});
}

// 新增、编辑弹框
const tableDialogRef = ref(null);
// 打开表格操作弹框
function openDialog(data = {}) {
	tableDialogRef.value.openDialog(data);
}

// 详情弹框引用
const detailDialogRef = ref(null);

// 打开详情弹框
const openDetailDialog = (row) => {
	detailDialogRef.value?.openDialog(row);
};

getTableList();
</script>

<style scoped>
.statistics-row {
	margin-bottom: 15px;
	padding: 10px 15px;
	background-color: #f8f8f8;
	border-radius: 4px;
	border-left: 4px solid #409eff;
}

.statistics-text {
	margin: 5px 0;
	font-size: 14px;
	color: #333;
	line-height: 24px;
}

/* Existing styles below */
</style>
