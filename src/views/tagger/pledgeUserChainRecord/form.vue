<template>
  <div v-if="dialogData.isShow">
    <el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="45%">
      <el-form ref="formRef" :rules="rules" :model="form" label-width="100px" v-loading="formLoading">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" clearable placeholder="请选择类型">
                <el-option label="质押" :value="1" />
                <el-option label="取消质押" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="用户ID" prop="userId">
              <el-input-number v-model="form.userId" placeholder="请输入用户ID"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="活动ID" prop="pledgeActivityId">
              <el-input-number v-model="form.pledgeActivityId" placeholder="请输入活动ID"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="质押积分" prop="pledgeScore">
              <el-input-number v-model="form.pledgeScore" placeholder="请输入质押积分"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="链上唯一hash值" prop="chainHash">
              <el-input v-model="form.chainHash" :maxlength="200" clearable placeholder="请输入链上唯一hash值"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="交易记录" prop="result">
              <el-input v-model="form.result" clearable placeholder="请输入交易记录" type="textarea" :autosize="{ minRows: 2}"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" :maxlength="200" clearable placeholder="请输入备注"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-debounce="submit">确定</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {addPledgeUserChainRecord, getPledgeUserChainRecord, updatePledgeUserChainRecord} from "@/api/tagger/pledgeUserChainRecord";
import {ElMessage} from "element-plus";
import {reactive, ref} from "vue";


const formRef = ref();

const emits = defineEmits(['refresh']);

// 表单
let addDefaultForm = {

}
let form = ref({});

let formLoading = ref(false);

// 效验规则
const rules = {
      type: [
        {required: true, message: '请选择类型', trigger: 'change'},
      ],
      pledgeScore: [
        {required: true, message: '请输入质押积分', trigger: 'blur'},
      ],
      chainHash: [
        {required: true, message: '请输入链上唯一hash值', trigger: 'blur'},
      ],
}

// 获取详情
const getDetails = (id) => {
  formLoading.value = true;
  getPledgeUserChainRecord(id).then((res) => {
    formLoading.value = false;
    form.value = Object.assign({}, form.value, res);
  });
};

// 弹框数据
const dialogData = reactive({
  isShow: false,
  title: '新增',
  id: null,
});

// 打开弹框
const openDialog = async (row) => {
  dialogData.isShow = true;
  dialogData.title = '新增质押用户链上记录';
  if (row?.id) {
    dialogData.id = row.id;
    dialogData.title = '编辑质押用户链上记录';
    form.value = {};
    getDetails(row.id);
  }else{
    form.value = addDefaultForm;
  }
};

// 关闭弹框
const closeDialog = () => {
  dialogData.isShow = false;
  dialogData.id = null;
  formRef.value.resetFields();
};

// 提交
const submit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      if (dialogData.id) {
        updatePledgeUserChainRecord(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      } else {
        addPledgeUserChainRecord(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      }
    }
  })

}

defineExpose({
  openDialog,
});
</script>