<template>
	<el-dialog v-model="drawerVisible" destroy-on-close title="用户详情" @close="handleClose" draggable width="900px">
		<div v-loading="loading" class="drawer-content">
			<!-- 基本信息 -->
			<div class="section">
				<div class="section-title">基本信息</div>
				<div class="section-content">
					<el-descriptions :column="2" border>
						<el-descriptions-item label="用户钱包地址">{{ userInfo.walletAddress }}</el-descriptions-item>
						<el-descriptions-item label="钱包类型">{{ userInfo.walletType }}</el-descriptions-item>
						<el-descriptions-item label="用户角色">{{ getUserRoleType(userInfo.userRoleId) }}</el-descriptions-item>
						<el-descriptions-item label="账号等级">{{ userInfo.vipLevel }}</el-descriptions-item>
						<el-descriptions-item label="状态">
							<el-radio-group v-model="userInfo.status" size="small" @change="handleStatusChange">
								<el-radio :label="1">正常</el-radio>
								<el-radio :label="0">禁用</el-radio>
							</el-radio-group>
						</el-descriptions-item>
						<el-descriptions-item label="创建时间">{{ userInfo.createTime }}</el-descriptions-item>
						<el-descriptions-item label="twitter账号">{{ userInfo.twitterAccount || '--' }}</el-descriptions-item>
						<el-descriptions-item label="邮箱地址">{{ userInfo.email || '--' }}</el-descriptions-item>
					</el-descriptions>
				</div>
			</div>

			<!-- CLAIM统计 -->
			<div class="section">
				<div class="section-title">CLAIM统计</div>
				<div class="section-content">
					<el-descriptions :column="3" border>
						<el-descriptions-item label="历史获得总代币量">{{ userInfo.totalHistoryTokenNum }}</el-descriptions-item>
						<el-descriptions-item label="已CLAIM代币累积量">{{ userInfo.totalClaimTokenNum }}</el-descriptions-item>
						<el-descriptions-item label="未claim量">{{ userInfo.notClaimTokenNum }}</el-descriptions-item>
					</el-descriptions>
				</div>
			</div>

			<!-- 任务统计 -->
			<div class="section">
				<div class="section-title">任务统计</div>
				<div class="section-content">
					<div class="task-stats">
						<div class="task-row">
							<div class="task-item">今日完成ai辅助标注任务数量: {{ userInfo.todayFinishAiTaskNum }}</div>
							<div class="task-item">今日完成人工标注任务数量: {{ userInfo.todayFinishManualTaskNum }}</div>
							<div class="task-item">今日完成审核量: {{ userInfo.todayFinishPicAuditTaskNum }}</div>
							<div class="task-item">今日完成任务链数量: {{ userInfo.todayFinishTaskChainNum }}</div>
						</div>

						<div class="task-total">总得分任务数量合计: {{ userInfo.totalTaskNum || '--' }}</div>

						<div class="task-row">
							<div class="task-item">当前完成ai辅助标注任务量排名 (7d): {{ userInfo.finishAiTaskNumTop || '--' }}</div>
							<div class="task-item">当前完成人工标注任务量排名 (7d): {{ userInfo.finishManualTaskNumTop || '--' }}</div>
							<div class="task-item">当前审核量排名 (7d): {{ userInfo.finishPicAuditTaskNumTop || '--' }}</div>
							<div class="task-item">当前奖励排名 (7d): {{ userInfo.rewardTop || '--' }}</div>
						</div>
					</div>

					<!-- 天数选择按钮 -->
					<div class="days-selector">
						<span class="days-label">统计周期：</span>
						<el-radio-group v-model="selectedDays" @change="handleDaysChange">
							<el-radio-button :label="7">最近7天</el-radio-button>
							<el-radio-button :label="30">最近30天</el-radio-button>
						</el-radio-group>
					</div>

					<!-- 图表展示区 -->
					<div class="charts-container">
						<div class="chart-section">
							<div class="chart-placeholder" v-loading="chartLoading">
								<el-empty v-if="rewardStats.length === 0 && !chartLoading" description="暂无数据" />
								<div ref="rewardChart" v-else class="chart"></div>
							</div>
							<div class="chart-footer">总得分任务数量: {{ userInfo.totalTaskNum }}</div>
						</div>

						<div class="chart-section">
							<h3>总完成ai标注任务数量: {{ userInfo.totalAiTaskNum }}</h3>
							<div class="chart-placeholder" v-loading="chartLoading">
								<el-empty v-if="aiTaskStats.length === 0 && !chartLoading" description="暂无数据" />
								<div ref="aiTaskChart" v-else class="chart"></div>
							</div>
							<div class="chart-footer">准确率 (现有ai判断): {{ userInfo.aiAccuracy }}%</div>
						</div>

						<div class="chart-section">
							<h3>总通过审核人工标注任务数量: {{ userInfo.totalAuditManualTaskNum }}</h3>
							<div class="chart-placeholder" v-loading="chartLoading">
								<el-empty v-if="manualTaskStats.length === 0 && !chartLoading" description="暂无数据" />
								<div ref="manualTaskChart" v-else class="chart"></div>
							</div>
							<div class="chart-footer">被审核通过率 (其他用户审查): {{ userInfo.approvedRate }}%</div>
						</div>

						<div class="chart-section">
							<h3>总完成图片审核且获得积分任务数量: {{ userInfo.totalPicAuditTaskNum }}</h3>
							<div class="chart-placeholder" v-loading="chartLoading">
								<el-empty v-if="auditTaskStats.length === 0 && !chartLoading" description="暂无数据" />
								<div ref="auditTaskChart" v-else class="chart"></div>
							</div>
							<div class="chart-footer">审核有效率 (审核他人图片并得分): {{ userInfo.auditValidRate }}%</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</el-dialog>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue';
import { getUser, getUserStatisticsApi, updateUserStatusApi } from '@/api/tagger/user';
import * as echarts from 'echarts';
import { getUserRoleType } from './constants';
import { ElMessage } from 'element-plus';

const props = defineProps({
	userId: {
		type: [Number, String],
		default: 0,
	},
});

const drawerVisible = ref(false);
const loading = ref(false);
const chartLoading = ref(false);
const userInfo = ref({});
const selectedDays = ref(7); // 默认显示7天数据

// 图表数据
const aiTaskStats = ref([]);
const manualTaskStats = ref([]);
const auditTaskStats = ref([]);
const rewardStats = ref([]); // 奖励排名数据

// 图表实例
let aiTaskChartInstance = null;
let manualTaskChartInstance = null;
let auditTaskChartInstance = null;
let rewardChartInstance = null;

// 图表DOM引用
const aiTaskChart = ref(null);
const manualTaskChart = ref(null);
const auditTaskChart = ref(null);
const rewardChart = ref(null);

// 获取用户详情
const getUserDetail = async (id) => {
	if (!id) return;

	loading.value = true;
	try {
		const res = await getUser(id);
		userInfo.value = res || {};
	} catch (error) {
		console.error('获取用户详情失败:', error);
	} finally {
		loading.value = false;
	}
};

// 处理天数变更
const handleDaysChange = (days) => {
	if (props.userId) {
		getUserStatistics(props.userId);
	}
};

// 获取统计数据
const getUserStatistics = async (userId) => {
	if (!userId) return;

	chartLoading.value = true;
	try {
		// 获取AI辅助标注任务统计
		const aiStats = await getUserStatisticsApi({
			userId,
			day: selectedDays.value,
			scoreType: 1,
		});
		aiTaskStats.value = aiStats || [];

		// 获取人工标注任务统计
		const manualStats = await getUserStatisticsApi({
			userId,
			day: selectedDays.value,
			scoreType: 2,
		});
		manualTaskStats.value = manualStats || [];

		// 获取图片审核任务统计
		const auditStats = await getUserStatisticsApi({
			userId,
			day: selectedDays.value,
			scoreType: 3,
		});
		auditTaskStats.value = auditStats || [];

		// 获取奖励排名统计
		const rewardsData = await getUserStatisticsApi({
			userId,
			day: selectedDays.value,
			scoreType: null,
		});
		rewardStats.value = rewardsData || [];

		// 等待DOM更新后初始化图表
		nextTick(() => {
			initCharts();
		});
	} catch (error) {
		console.error('获取统计数据失败:', error);
	} finally {
		chartLoading.value = false;
	}
};

// 初始化图表
const initCharts = () => {
	// 初始化AI辅助标注任务图表
	if (aiTaskStats.value.length > 0 && aiTaskChart.value) {
		if (aiTaskChartInstance) {
			aiTaskChartInstance.dispose();
		}
		aiTaskChartInstance = echarts.init(aiTaskChart.value);
		const option = getChartOption(aiTaskStats.value, '任务数量');
		aiTaskChartInstance.setOption(option);
	}

	// 初始化人工标注任务图表
	if (manualTaskStats.value.length > 0 && manualTaskChart.value) {
		if (manualTaskChartInstance) {
			manualTaskChartInstance.dispose();
		}
		manualTaskChartInstance = echarts.init(manualTaskChart.value);
		const option = getChartOption(manualTaskStats.value, '任务数量');
		manualTaskChartInstance.setOption(option);
	}

	// 初始化图片审核任务图表
	if (auditTaskStats.value.length > 0 && auditTaskChart.value) {
		if (auditTaskChartInstance) {
			auditTaskChartInstance.dispose();
		}
		auditTaskChartInstance = echarts.init(auditTaskChart.value);
		const option = getChartOption(auditTaskStats.value, '任务数量');
		auditTaskChartInstance.setOption(option);
	}

	// 初始化奖励排名图表
	if (rewardStats.value.length > 0 && rewardChart.value) {
		if (rewardChartInstance) {
			rewardChartInstance.dispose();
		}
		rewardChartInstance = echarts.init(rewardChart.value);
		const option = getChartOption(rewardStats.value, '任务数量');
		rewardChartInstance.setOption(option);
	}
};

// 图表配置
const getChartOption = (data, yAxisName) => {
	const xAxisData = data.map((item) => item.label);
	const seriesData = data.map((item) => item.value);

	return {
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow',
			},
		},
		grid: {
			left: '3%',
			right: '4%',
			bottom: '3%',
			containLabel: true,
		},
		xAxis: {
			type: 'category',
			data: xAxisData,
			axisLabel: {
				rotate: 45,
			},
		},
		yAxis: {
			type: 'value',
			name: yAxisName,
		},
		series: [
			{
				name: yAxisName,
				type: 'bar',
				data: seriesData,
				itemStyle: {
					color: '#409EFF',
				},
			},
		],
	};
};

// 打开抽屉
const openDrawer = (userId) => {
	drawerVisible.value = true;
	getUserDetail(userId);
	getUserStatistics(userId);
};

// 关闭抽屉
const handleClose = () => {
	drawerVisible.value = false;
	userInfo.value = {};
	selectedDays.value = 30; // 重置为默认30天

	// 销毁图表实例
	if (aiTaskChartInstance) {
		aiTaskChartInstance.dispose();
		aiTaskChartInstance = null;
	}
	if (manualTaskChartInstance) {
		manualTaskChartInstance.dispose();
		manualTaskChartInstance = null;
	}
	if (auditTaskChartInstance) {
		auditTaskChartInstance.dispose();
		auditTaskChartInstance = null;
	}
	if (rewardChartInstance) {
		rewardChartInstance.dispose();
		rewardChartInstance = null;
	}
};

// 监听窗口大小变化，调整图表大小
window.addEventListener('resize', () => {
	if (aiTaskChartInstance) aiTaskChartInstance.resize();
	if (manualTaskChartInstance) manualTaskChartInstance.resize();
	if (auditTaskChartInstance) auditTaskChartInstance.resize();
	if (rewardChartInstance) rewardChartInstance.resize();
});

// 组件卸载时清理事件监听
onMounted(() => {
	return () => {
		window.removeEventListener('resize', () => {});
		if (aiTaskChartInstance) aiTaskChartInstance.dispose();
		if (manualTaskChartInstance) manualTaskChartInstance.dispose();
		if (auditTaskChartInstance) auditTaskChartInstance.dispose();
		if (rewardChartInstance) rewardChartInstance.dispose();
	};
});

// 向父组件暴露openDrawer方法
defineExpose({
	openDrawer,
});

// 处理状态变更
const handleStatusChange = async (newStatus) => {
	if (!userInfo.value || !userInfo.value.id) return;
	
	loading.value = true;
	try {
		await updateUserStatusApi({
			userId: userInfo.value.id,
			status: newStatus
		});
		userInfo.value.status = newStatus;
		ElMessage.success(`用户状态已${newStatus === 1 ? '启用' : '禁用'}`);
	} catch (error) {
		// 如果接口调用失败，恢复原来的状态
		userInfo.value.status = userInfo.value.status === 1 ? 0 : 1;
		ElMessage.error(`状态修改失败: ${error.message || '未知错误'}`);
	} finally {
		loading.value = false;
	}
};
</script>

<style scoped>
.drawer-content {
	padding: 20px;
}

.section {
	margin-bottom: 24px;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 16px;
	position: relative;
	padding-left: 12px;
	background-color: #d1fffd;
	line-height: 35px;
}

/* .section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #409eff;
  border-radius: 2px;
} */

.section-content {
	background-color: #f5f7fa;
	border-radius: 4px;
	padding: 16px;
}

.task-stats {
	display: flex;
	flex-direction: column;
	gap: 20px;
}

.task-row {
	display: flex;
	flex-wrap: wrap;
	gap: 20px;
}

.task-item {
	flex: 1;
	min-width: 200px;
}

.task-total {
	font-size: 16px;
	font-weight: bold;
	margin: 15px 0;
}

.days-selector {
	margin: 20px 0;
	text-align: center;
}

.days-label {
	margin-right: 10px;
	font-weight: bold;
}

.charts-container {
	display: flex;
	flex-direction: column;
	gap: 40px;
	margin-top: 30px;
}

.chart-section {
	border: 1px solid #e0e0e0;
	border-radius: 4px;
	padding: 15px;
	background-color: #fff;
}

.chart-section h3 {
	margin-top: 0;
	margin-bottom: 15px;
	font-size: 14px;
	text-align: center;
}

.chart-placeholder {
	height: 300px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.chart {
	width: 100%;
	height: 100%;
}

.chart-footer {
	margin-top: 15px;
	text-align: center;
	font-weight: bold;
}
</style>
