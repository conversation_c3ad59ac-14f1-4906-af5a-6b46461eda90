<template>
	<el-card shadow="never" class="card-box">
		<el-form :model="queryForm" label-width="100px">
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="用户角色">
						<el-select v-model="queryForm.userRoleId" clearable placeholder="请选择用户角色">
							<el-option v-for="item in userRoleType" :key="item.value" :label="item.label" :value="item.value" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="任务类型">
						<el-select v-model="queryForm.taskType" clearable placeholder="请选择任务类型">
							<el-option label="有做过AI辅助标注" :value="1" />
							<el-option label="有做过手工标注" :value="2" />
							<el-option label="有做过审核任务" :value="3" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="状态">
						<el-select v-model="queryForm.status" clearable placeholder="请选择状态">
							<el-option label="正常" :value="1" />
							<el-option label="禁用" :value="0" />
						</el-select>
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="钱包地址">
						<el-input v-model="queryForm.walletAddress" @keyup.enter="onSearch" clearable placeholder="请输入钱包地址" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label-width="0">
						<el-button type="primary" @click="onSearch">
							<el-icon>
								<ele-search />
							</el-icon>
							<span class="search-btn__left">查询</span>
						</el-button>
						<el-button @click="onReset">
							<el-icon>
								<ele-refresh />
							</el-icon>
							<span class="search-btn__left">重置</span>
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div v-auth="'user:add'" class="table-btn-box mb-3">
			<el-button type="primary" @click="openDialog">
				<el-icon class="mr-1">
					<ele-circle-plus />
				</el-icon>
				新 增
			</el-button>
		</div>
		<el-table v-loading="tableData.isLoading" :data="tableData.data" @sort-change="sortChange" border row-key="id">
			<el-table-column prop="number" label="序号" align="center" width="60px" />
			<!-- <el-table-column prop="username" label="账号" align="center" />
			<el-table-column prop="nickname" label="昵称" align="center" /> -->
			<el-table-column prop="walletAddress" label="钱包地址" align="center" width="400" />
			<!-- <el-table-column prop="walletType" label="钱包类型" align="center" width="120" /> -->
			<!-- <el-table-column prop="twitterAccount" label="Twitter账号" align="center" /> -->
			<!-- <el-table-column prop="accountLevelId" label="账号等级" align="center" /> -->
			<!-- <el-table-column prop="totalTokenNum" label="已获代币累计量" align="center" /> -->
			<!-- <el-table-column prop="userRoleId" label="用户角色" align="center" width="180">
				<template #default="scope">
					<span>{{ getUserRoleType(scope.row.userRoleId) }}</span>
				</template>
			</el-table-column> -->
			<!-- <el-table-column prop="status" label="状态" align="center">
				<template #default="scope">
					<span v-if="scope.row.status == 1">正常</span>
					<span v-if="scope.row.status == 0">禁用</span>
				</template>
			</el-table-column> -->
			<el-table-column prop="totalTaskNum" label="总得分任务数量合计" align="center" sortable="custom" />
			<el-table-column prop="totalAiTaskNum" label="总完成ai辅助标注任务数量" align="center" sortable="custom" />
			<el-table-column prop="totalAuditManualTaskNum" label="总通过审核人工标注任务数量" align="center" sortable="custom" />
			<el-table-column prop="aiAccuracy" label="准确率（现有ai判断）" align="center" />
			<el-table-column prop="approvedRate" label="被审核通过率（被其他用户审核）" align="center" sortable="custom" />
			<el-table-column prop="totalPicAuditTaskNum" label="总图片审核任务数量" align="center" sortable="custom" />
			<el-table-column prop="auditValidRate" label="审核有效率" align="center" sortable="custom" />
			<el-table-column prop="todayFinishAiTaskNum" label="今日完成ai辅助标注任务数量" align="center" sortable="custom" />
			<el-table-column prop="todayFinishManualTaskNum" label="今日完成人工标注任务数量" align="center" sortable="custom" />
			<el-table-column prop="todayFinishPicAuditTaskNum" label="今日完成图片审核任务数量" align="center" sortable="custom" />
			<el-table-column prop="totalHistoryTokenNum" label="历史获得总代币量" align="center" sortable="custom" />
			<el-table-column prop="totalClaimTokenNum" label="已claim代币累积量" align="center" sortable="custom" />
			<el-table-column prop="notClaimTokenNum" label="未claim量" align="center" sortable="custom" />
			<!-- <el-table-column prop="email" label="邮箱地址" align="center" width="200" /> -->
			<!-- <el-table-column prop="remark" label="备注" align="center" /> -->
			<!-- <el-table-column prop="createTime" label="创建时间" align="center" min-width="150" sortable="custom" width="200" /> -->
			<el-table-column label="操作" width="120" align="center" fixed="right">
				<template #default="{ row }">
					<div class="flex-gap">
						<el-button type="primary" link @click="showUserDetail(row)"> 查看详情 </el-button>
					</div>
				</template>
			</el-table-column>
		</el-table>

		<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

		<TableForm ref="tableDialogRef" @refresh="getTableList" />

		<!-- 用户详情抽屉 -->
		<user-detail ref="userDetailRef" :userId="selectedUserId" />
	</el-card>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { deleteUser, getUserPage } from '@/api/tagger/user';
import TableForm from './form.vue';
import { reactive, ref } from 'vue';
import UserDetail from './user-detail.vue';
import { userRoleType, getUserRoleType } from './constants';

let queryForm = ref({});
// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {};
	pageData.pageNum = 1;
	getTableList();
};

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

// 排序
const orderBy = ref({});
const sortChange = ({ prop, order }) => {
	if (order) {
		orderBy.value.orderByColumn = prop;
		orderBy.value.orderByAsc = order === 'ascending';
	} else {
		orderBy.value = {};
	}
	pageData.pageNum = 1;
	getTableList();
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 获取表格列表
const getTableList = () => {
	tableData.isLoading = true;
	getUserPage({ ...pageData, ...queryForm.value, ...orderBy.value, total: undefined }).then((res) => {
		tableData.data = res?.list?.map((p, i) => {
			p.number = 1 + i + (pageData.pageNum - 1) * pageData.pageSize;
			return p;
		});
		pageData.total = res?.total;
		tableData.isLoading = false;
	});
};

// 删除列表数据
function delTable(row) {
	ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deleteUser(row.id).then(() => {
				ElMessage.success('删除成功');
				getTableList();
			});
		})
		.catch(() => {});
}

// 新增、编辑弹框
const tableDialogRef = ref(null);
// 打开表格操作弹框
function openDialog(data = {}) {
	tableDialogRef.value.openDialog(data);
}

// 用户详情抽屉
const userDetailRef = ref(null);
const selectedUserId = ref(null);

// 显示用户详情
const showUserDetail = (row) => {
	selectedUserId.value = row.id;
	userDetailRef.value.openDrawer(row.id);
};

getTableList();
</script>
