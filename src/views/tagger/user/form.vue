<template>
  <div v-if="dialogData.isShow">
    <el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="45%">
      <el-form ref="formRef" :rules="rules" :model="form" label-width="100px" v-loading="formLoading">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="账号" prop="username">
              <el-input v-model="form.username" :maxlength="1024" clearable placeholder="请输入账号" type="textarea" :autosize="{ minRows: 2}"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="昵称" prop="nickname">
              <el-input v-model="form.nickname" :maxlength="20" clearable placeholder="请输入昵称"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="钱包地址" prop="walletAddress">
              <el-input v-model="form.walletAddress" :maxlength="200" clearable placeholder="请输入钱包地址"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="钱包类型" prop="walletType">
              <el-input v-model="form.walletType" :maxlength="255" clearable placeholder="请输入钱包类型"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="Twitter账号" prop="twitterAccount">
              <el-input v-model="form.twitterAccount" :maxlength="100" clearable placeholder="请输入Twitter账号"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="账号等级" prop="accountLevelId">
              <el-input-number v-model="form.accountLevelId" placeholder="请输入账号等级"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已获代币累计量" prop="totalTokenNum">
              <el-input-number v-model="form.totalTokenNum" placeholder="请输入已获代币累计量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="密码" prop="password">
              <el-input v-model="form.password" :maxlength="64" clearable placeholder="请输入密码"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="盐值" prop="salt">
              <el-input v-model="form.salt" :maxlength="32" clearable placeholder="请输入盐值"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="用户角色ID" prop="userRoleId">
              <el-input-number v-model="form.userRoleId" placeholder="请输入用户角色ID"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" clearable placeholder="请选择状态">
                <el-option label="正常" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="总得分任务数量合计" prop="totalTaskNum">
              <el-input-number v-model="form.totalTaskNum" placeholder="请输入总得分任务数量合计"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="总完成ai辅助标注任务数量" prop="totalAiTaskNum">
              <el-input-number v-model="form.totalAiTaskNum" placeholder="请输入总完成ai辅助标注任务数量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="总通过审核人工标注任务数量" prop="totalAuditManualTaskNum">
              <el-input-number v-model="form.totalAuditManualTaskNum" placeholder="请输入总通过审核人工标注任务数量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="准确率（现有ai判断）" prop="aiAccuracy">
              <el-input-number v-model="form.aiAccuracy" placeholder="请输入准确率（现有ai判断）"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="被审核通过率（被其他用户审核）" prop="approvedRate">
              <el-input-number v-model="form.approvedRate" placeholder="请输入被审核通过率（被其他用户审核）"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="总图片审核任务数量" prop="totalPicAuditTaskNum">
              <el-input-number v-model="form.totalPicAuditTaskNum" placeholder="请输入总图片审核任务数量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核有效率" prop="auditValidRate">
              <el-input-number v-model="form.auditValidRate" placeholder="请输入审核有效率"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="今日完成ai辅助标注任务数量" prop="todayFinishAiTaskNum">
              <el-input-number v-model="form.todayFinishAiTaskNum" placeholder="请输入今日完成ai辅助标注任务数量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="今日完成人工标注任务数量" prop="todayFinishManualTaskNum">
              <el-input-number v-model="form.todayFinishManualTaskNum" placeholder="请输入今日完成人工标注任务数量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="今日完成图片审核任务数量" prop="todayFinishPicAuditTaskNum">
              <el-input-number v-model="form.todayFinishPicAuditTaskNum" placeholder="请输入今日完成图片审核任务数量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="历史获得总代币量" prop="totalHistoryTokenNum">
              <el-input-number v-model="form.totalHistoryTokenNum" placeholder="请输入历史获得总代币量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="已claim代币累积量" prop="totalClaimTokenNum">
              <el-input-number v-model="form.totalClaimTokenNum" placeholder="请输入已claim代币累积量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="未claim量" prop="notClaimTokenNum">
              <el-input-number v-model="form.notClaimTokenNum" placeholder="请输入未claim量"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="邮箱地址" prop="email">
              <el-input v-model="form.email" :maxlength="200" clearable placeholder="请输入邮箱地址"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="最后登录时间" prop="lastLoginTime">
              <date-picker type="datetime" v-model="form.lastLoginTime" clearable placeholder="请选择最后登录时间"/>
             </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" :maxlength="200" clearable placeholder="请输入备注"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-debounce="submit">确定</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {addUser, getUser, updateUser} from "@/api/tagger/user";
import {ElMessage} from "element-plus";
import {reactive, ref} from "vue";


const formRef = ref();

const emits = defineEmits(['refresh']);

// 表单
let addDefaultForm = {

}
let form = ref({});

let formLoading = ref(false);

// 效验规则
const rules = {
      username: [
        {required: true, message: '请输入账号', trigger: 'blur'},
      ],
      walletType: [
        {required: true, message: '请输入钱包类型', trigger: 'blur'},
      ],
      twitterAccount: [
        {required: true, message: '请输入Twitter账号', trigger: 'blur'},
      ],
      accountLevelId: [
        {required: true, message: '请输入账号等级', trigger: 'blur'},
      ],
      totalTokenNum: [
        {required: true, message: '请输入已获代币累计量', trigger: 'blur'},
      ],
      userRoleId: [
        {required: true, message: '请输入用户角色ID', trigger: 'blur'},
      ],
      status: [
        {required: true, message: '请选择状态', trigger: 'change'},
      ],
      totalTaskNum: [
        {required: true, message: '请输入总得分任务数量合计', trigger: 'blur'},
      ],
      totalAiTaskNum: [
        {required: true, message: '请输入总完成ai辅助标注任务数量', trigger: 'blur'},
      ],
      totalAuditManualTaskNum: [
        {required: true, message: '请输入总通过审核人工标注任务数量', trigger: 'blur'},
      ],
      aiAccuracy: [
        {required: true, message: '请输入准确率（现有ai判断）', trigger: 'blur'},
      ],
      approvedRate: [
        {required: true, message: '请输入被审核通过率（被其他用户审核）', trigger: 'blur'},
      ],
      totalPicAuditTaskNum: [
        {required: true, message: '请输入总图片审核任务数量', trigger: 'blur'},
      ],
      auditValidRate: [
        {required: true, message: '请输入审核有效率', trigger: 'blur'},
      ],
      todayFinishAiTaskNum: [
        {required: true, message: '请输入今日完成ai辅助标注任务数量', trigger: 'blur'},
      ],
      todayFinishManualTaskNum: [
        {required: true, message: '请输入今日完成人工标注任务数量', trigger: 'blur'},
      ],
      todayFinishPicAuditTaskNum: [
        {required: true, message: '请输入今日完成图片审核任务数量', trigger: 'blur'},
      ],
      totalHistoryTokenNum: [
        {required: true, message: '请输入历史获得总代币量', trigger: 'blur'},
      ],
      totalClaimTokenNum: [
        {required: true, message: '请输入已claim代币累积量', trigger: 'blur'},
      ],
      notClaimTokenNum: [
        {required: true, message: '请输入未claim量', trigger: 'blur'},
      ],
}

// 获取详情
const getDetails = (id) => {
  formLoading.value = true;
  getUser(id).then((res) => {
    formLoading.value = false;
    form.value = Object.assign({}, form.value, res);
  });
};

// 弹框数据
const dialogData = reactive({
  isShow: false,
  title: '新增',
  id: null,
});

// 打开弹框
const openDialog = async (row) => {
  dialogData.isShow = true;
  dialogData.title = '新增用户信息';
  if (row?.id) {
    dialogData.id = row.id;
    dialogData.title = '编辑用户信息';
    form.value = {};
    getDetails(row.id);
  }else{
    form.value = addDefaultForm;
  }
};

// 关闭弹框
const closeDialog = () => {
  dialogData.isShow = false;
  dialogData.id = null;
  formRef.value.resetFields();
};

// 提交
const submit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      if (dialogData.id) {
        updateUser(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      } else {
        addUser(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      }
    }
  })

}

defineExpose({
  openDialog,
});
</script>