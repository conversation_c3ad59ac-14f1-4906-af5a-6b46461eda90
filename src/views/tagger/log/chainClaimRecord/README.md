# 用户Claim积分上链记录页面

这个页面用于展示和管理用户Claim积分的上链记录信息。

## 功能

- 查看用户Claim积分上链记录
- 按多种条件筛选记录：
  - 时间范围（当日、本周、本月、全部）
  - 上链状态（全部、上链成功、上链失败）
  - 用户ID
  - 钱包地址
  - 关键词搜索
- 下载记录数据

## API接口

### 获取用户Claim积分上链记录列表
- URL: `/admin/chainClaimRecord/getChainClaimRecordPage`
- 方法: POST
- 参数:
  ```json
  {
    "orderByColumn": "",
    "orderByAsc": true,
    "pageNum": 1,
    "pageSize": 10,
    "keyword": "",
    "userId": 0,
    "walletAddress": "",
    "chainStatus": 0,
    "type": 1 // 1：当日，2：本周，3：本月，4：全部
  }
  ```

### 下载用户Claim积分上链记录
- URL: `/api/admin/chainClaimRecord/downloadChainClaimRecord/{type}&Authorization={token}`
- 方法: GET
- 参数:
  - type: 类型（1：当日，2：本周，3：本月，4：全部）
  - token: 认证令牌

## 数据字段说明

| 字段名 | 描述 | 类型 |
| ----- | ---- | ---- |
| id | ID | integer(int64) |
| userId | 用户ID | integer(int64) |
| walletAddress | 钱包地址 | string |
| score | 积分 | number |
| scoreZero | 带0积分 | string |
| chainStatus | 上链状态 2: 上链成功, 3: 上链失败 | integer(int32) |
| chainResultDate | 上链结果日期 | string(date-time) |
| chainResultTime | 上链结果时间 | string(date-time) |
| chainResultCode | 上链结果编码 200: 成功, 500: 失败 | integer(int32) |
| chainResultData | 上链结果数据 | string |
| chainResultMessage | 上链结果信息 | string |
| chainResultError | 上链结果错误信息 | string |
| chainResult | 上链结果 | string |
| remark | 备注 | string |
| createId | 创建人ID | integer(int64) |
| createTime | 创建时间 | string(date-time) |
| updateId | 修改人ID | integer(int64) |
| updateTime | 修改时间 | string(date-time) |

## 使用说明

1. 使用筛选条件缩小搜索范围：
   - 选择时间范围（当日、本周、本月、全部）
   - 选择上链状态（全部、上链成功、上链失败）
   - 输入用户ID或钱包地址进行精确查找
   - 使用关键词搜索其他字段
   
2. 点击"查询"按钮执行筛选，点击"重置"按钮清除筛选条件

3. 点击"下载数据"按钮可下载当前筛选条件下的所有记录 