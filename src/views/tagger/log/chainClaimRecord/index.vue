<template>
  <el-card shadow="never" class="card-box">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item label="时间范围">
          <el-radio-group v-model="queryParams.type" @change="handleTypeChange">
            <el-radio-button :label="1">当日</el-radio-button>
            <el-radio-button :label="2">本周</el-radio-button>
            <el-radio-button :label="3">本月</el-radio-button>
            <el-radio-button :label="4">全部</el-radio-button>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="上链状态">
          <el-select class="w-40" v-model="queryParams.chainStatus" clearable placeholder="请选择上链状态">
            <el-option label="全部" :value="0" />
            <el-option label="上链成功" :value="2" />
            <el-option label="上链失败" :value="3" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="用户ID">
          <el-input v-model.number="queryParams.userId" placeholder="请输入用户ID" clearable />
        </el-form-item>
        
        <el-form-item label="钱包地址">
          <el-input v-model="queryParams.walletAddress" placeholder="请输入钱包地址" clearable />
        </el-form-item>
        
        <el-form-item label="关键词">
          <el-input v-model="queryParams.keyword" placeholder="请输入关键词" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            查询
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格操作 -->
    <div class="table-operations">
      <el-button type="primary" @click="handleDownload">
        <el-icon><Download /></el-icon>
        下载数据
      </el-button>
    </div>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column type="index" label="序号" width="70" align="center" />
      <el-table-column prop="userId" label="用户ID" min-width="100" align="center" />
      <el-table-column prop="walletAddress" label="钱包地址" min-width="220" align="center" show-overflow-tooltip />
      <el-table-column prop="score" label="积分" min-width="100" align="center" />
      <el-table-column prop="scoreZero" label="带0积分" min-width="100" align="center" />
      <el-table-column prop="chainStatus" label="上链状态" min-width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="row.chainStatus === 2 ? 'success' : (row.chainStatus === 3 ? 'danger' : 'info')">
            {{ row.chainStatus === 2 ? '上链成功' : (row.chainStatus === 3 ? '上链失败' : '--') }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="chainResultDate" label="上链结果日期" min-width="120" align="center" />
      <el-table-column prop="chainResultTime" label="上链结果时间" min-width="120" align="center" />
      <el-table-column prop="chainResultCode" label="上链结果编码" min-width="120" align="center">
        <template #default="{ row }">
          <span>{{ formatResultCode(row.chainResultCode) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="chainResultData" label="上链结果数据" min-width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="chainResultMessage" label="上链结果信息" min-width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="chainResultError" label="上链结果错误信息" min-width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="chainResult" label="上链结果" min-width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="remark" label="备注" min-width="120" align="center" show-overflow-tooltip />
      <el-table-column prop="createTime" label="创建时间" min-width="150" align="center" />
      <el-table-column prop="updateTime" label="更新时间" min-width="150" align="center" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import { getChainClaimRecordPage, getDownloadUrl } from '@/api/tagger/chainClaimRecord';
import { getToken } from '@/utils/token';

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  userId: '',
  walletAddress: '',
  chainStatus: '',
  type: 1, // 默认当日
  orderByColumn: '',
  orderByAsc: true,
});

// 表格数据
const tableData = ref([]);
const total = ref(0);
const loading = ref(false);

// 获取表格数据
const getTableList = () => {
  loading.value = true;
  getChainClaimRecordPage(queryParams)
    .then((res) => {
      tableData.value = res.list || [];
      total.value = res.total || 0;
    })
    .catch((error) => {
      console.error('获取用户claim积分上链记录失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 格式化结果代码
const formatResultCode = (code) => {
  if (code === 200) return '200: 成功';
  if (code === 500) return '500: 失败';
  return code || '--';
};

// 处理类型变化
const handleTypeChange = () => {
  queryParams.pageNum = 1;
  getTableList();
};

// 搜索
const handleSearch = () => {
  queryParams.pageNum = 1;
  getTableList();
};

// 重置
const handleReset = () => {
  queryParams.keyword = '';
  queryParams.userId = '';
  queryParams.walletAddress = '';
  queryParams.chainStatus = '';
  queryParams.pageNum = 1;
  getTableList();
};

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  getTableList();
};

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page;
  getTableList();
};

// 下载数据
const handleDownload = () => {
  const url = getDownloadUrl(queryParams.type, getToken());
  window.open(url, '_blank');
};

// 初始化
onMounted(() => {
  getTableList();
});
</script>

<style scoped>
.card-box {
  margin-bottom: 20px;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.filter-form .el-form-item {
  margin-bottom: 15px;
  margin-right: 0;
}

.table-operations {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 