<template>
  <el-card shadow="never" class="card-box">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-radio-group v-model="queryParams.type" @change="handleTypeChange">
        <el-radio-button :label="1">当日</el-radio-button>
        <el-radio-button :label="2">本周</el-radio-button>
        <el-radio-button :label="3">本月</el-radio-button>
        <el-radio-button :label="4">全部</el-radio-button>
      </el-radio-group>
      
      <el-form :inline="true" :model="queryParams" class="filter-form">
        <el-form-item>
          <el-input v-model="queryParams.keyword" placeholder="搜索关键词" clearable @keyup.enter="handleSearch" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格操作 -->
    <div class="table-operations">
      <el-button type="primary" @click="handleDownload">
        <el-icon><Download /></el-icon>
        下载数据
      </el-button>
    </div>

    <!-- 表格区域 -->
    <el-table v-loading="loading" :data="tableData" border style="width: 100%">
      <el-table-column type="index" label="序号" width="70" align="center" />
      <el-table-column prop="eventName" label="事件名称" min-width="150" align="center" />
      <el-table-column prop="user" label="用户地址" min-width="220" align="center" />
      <el-table-column prop="amount" label="数量" min-width="120" align="center" />
      <el-table-column prop="score" label="分数" min-width="100" align="center" />
      <el-table-column prop="transactionTime" label="交易时间" min-width="180" align="center" />
      <el-table-column prop="blockNumber" label="区块号" min-width="120" align="center" />
      <el-table-column prop="transactionHash" label="交易哈希" min-width="220" align="center">
        <template #default="{ row }">
          <el-tooltip :content="row.transactionHash" placement="top" :show-after="500">
            <span class="truncated-text">{{ row.transactionHash }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150" align="center" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="queryParams.pageNum"
        :page-size="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Search, Refresh, Download } from '@element-plus/icons-vue';
import { getChainPledgeActivityRecordPage, getDownloadUrl } from '@/api/tagger/chainPledgeActivityRecord';
import { getToken } from '@/utils/token';

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  keyword: '',
  type: 1, // 默认当日
  orderByColumn: '',
  orderByAsc: true,
});

// 表格数据
const tableData = ref([]);
const total = ref(0);
const loading = ref(false);

// 获取表格数据
const getTableList = () => {
  loading.value = true;
  getChainPledgeActivityRecordPage(queryParams)
    .then((res) => {
      tableData.value = res.list || [];
      total.value = res.total || 0;
    })
    .catch((error) => {
      console.error('获取质押活动链上记录失败:', error);
    })
    .finally(() => {
      loading.value = false;
    });
};

// 处理类型变化
const handleTypeChange = () => {
  queryParams.pageNum = 1;
  getTableList();
};

// 搜索
const handleSearch = () => {
  queryParams.pageNum = 1;
  getTableList();
};

// 重置
const handleReset = () => {
  queryParams.keyword = '';
  queryParams.pageNum = 1;
  getTableList();
};

// 分页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  getTableList();
};

// 页码变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page;
  getTableList();
};

// 下载数据
const handleDownload = () => {
  const url = getDownloadUrl(queryParams.type, getToken());
  window.open(url, '_blank');
};

// 初始化
onMounted(() => {
  getTableList();
});
</script>

<style scoped>
.card-box {
  margin-bottom: 20px;
}

.filter-section {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;
  gap: 15px;
}

.filter-form {
  display: flex;
  align-items: center;
}

.table-operations {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.truncated-text {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style> 