# 质押活动链上任务数量记录页面

这个页面用于展示和管理质押活动的链上任务数量记录。

## 功能

- 查看质押活动链上任务数量记录
- 按时间范围过滤记录（当日、本周、本月、全部）
- 关键字搜索
- 下载记录数据

## API接口

### 获取质押活动链上任务数量记录分页列表
- URL: `/admin/chainUserTaskNumRecord/getChainUserTaskNumRecordPage`
- 方法: POST
- 参数:
  ```json
  {
    "orderByColumn": "",
    "orderByAsc": true,
    "pageNum": 1,
    "pageSize": 10,
    "keyword": "",
    "type": 1 // 1：当日，2：本周，3：本月，4：全部
  }
  ```

### 下载质押活动链上任务数量记录
- URL: `/api/admin/chainUserTaskNumRecord/downloadChainUserTaskNumRecord/{type}&Authorization={token}`
- 方法: GET
- 参数:
  - type: 类型（1：当日，2：本周，3：本月，4：全部）
  - token: 认证令牌

## 数据字段说明

| 字段名 | 描述 | 类型 |
| ----- | ---- | ---- |
| id | ID | integer(int64) |
| eventName | 事件名称 | string |
| taskId | 任务数量上链ID对应_chain_user_task_num表主键ID | string |
| user | 用户钱包地址 | string |
| taskNumber | 任务数量 | integer(int32) |
| date | 上链日期 | string |
| workHash | work_hash | string |
| timestamp | 交易时间戳 | integer(int32) |
| transactionTime | 交易时间 | string(date-time) |
| blockNumber | 区块数 | integer(int32) |
| transactionHash | 唯一交易hash | string |
| remark | 备注 | string |
| createId | 创建人ID | integer(int64) |
| createTime | 创建时间 | string(date-time) |
| updateId | 修改人ID | integer(int64) |
| updateTime | 修改时间 | string(date-time) |

## 使用说明

1. 选择时间范围（当日、本周、本月、全部）
2. 可选择使用关键字搜索
3. 点击"下载数据"按钮可下载当前筛选条件下的所有记录 