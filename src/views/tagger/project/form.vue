<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="700px">
			<el-form ref="formRef" :rules="rules" :model="form" label-width="140px" v-loading="formLoading">
				<el-row :gutter="10">
					<el-col :span="24">
						<el-form-item label="数据集名称" prop="name">
							<el-input v-model="form.name" :maxlength="100" clearable placeholder="请输入数据集名称" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="关键字" prop="projectKeyword">
							<div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center">
								<el-tag v-for="tag in keywordTags" :key="tag" closable @close="handleKeywordClose(tag)">
									{{ tag }}
								</el-tag>
								<el-input v-if="keywordInputVisible" ref="keywordInputRef" v-model="keywordInputValue" class="tag-input" size="small" @keyup.enter="handleKeywordConfirm" @blur="handleKeywordConfirm" />
								<el-button v-else class="button-new-tag" size="small" @click="showKeywordInput">
									<el-icon><Plus /></el-icon>
								</el-button>
							</div>
						</el-form-item>
					</el-col>
					<el-col v-if="projectType === 2" :span="24">
						<el-form-item label="标签名" prop="targetLabel">
							<div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center">
								<el-tag v-for="tag in labelTags" :key="tag" closable @close="handleLabelClose(tag)">
									{{ tag }}
								</el-tag>
								<el-input v-if="labelInputVisible" ref="labelInputRef" v-model="labelInputValue" class="tag-input" size="small" @keyup.enter="handleLabelConfirm" @blur="handleLabelConfirm" />
								<el-button v-else class="button-new-tag" size="small" @click="showLabelInput">
									<el-icon><Plus /></el-icon>
								</el-button>
							</div>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="单任务标注积分" prop="tagScore">
							<el-input-number v-model="form.tagScore" placeholder="请输入单任务标注积分" :controls="false" :min="0">
								<template #suffix>
									<span style="margin-left: 5px">TAG</span>
								</template>
							</el-input-number>
						</el-form-item></el-col
					>
					<el-col v-if="projectType === 2" :span="12">
						<el-form-item label="单任务审核积分" prop="auditScore">
							<el-input-number v-model="form.auditScore" placeholder="请输入单任务审核积分" :controls="false" :min="0">
								<template #suffix>
									<span style="margin-left: 5px">TAG</span>
								</template>
							</el-input-number>
						</el-form-item></el-col
					>
					<el-col :span="24">
						<el-form-item label="上传本地样例图片" prop="coverImg">
							<el-upload ref="uploadRef" :action="baseApiPath + '/common/upload'" list-type="picture-card" :headers="{ Authorization: getToken() }" :limit="1" :before-upload="beforeUpload" :on-success="handleUploadSuccess" :on-error="handleUploadError" :on-exceed="handleExceed" accept="image/*">
								<el-icon><Plus /></el-icon>
							</el-upload>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" v-debounce="submit">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue';
import { addProjectApi, getProjectApi, updateProjectApi } from '@/api/tagger/project';
import { ElMessage } from 'element-plus';
import { reactive, ref, nextTick } from 'vue';
import { getToken } from '@/utils/token.js';
import { cloneDeep } from 'lodash-es';

const baseApiPath = import.meta.env.VITE_BASE_URL;
const formRef = ref();
const props = defineProps({ projectType: Number });
const emits = defineEmits(['refresh']);

// 关键字和标签输入值
const keywordInputValue = ref('');
const labelInputValue = ref('');
const keywordTags = ref([]);
const labelTags = ref([]);

// 表单
let addDefaultForm = {};
let form = ref(cloneDeep(addDefaultForm));

let formLoading = ref(false);

// 输入框显示状态
const keywordInputVisible = ref(false);
const labelInputVisible = ref(false);
const keywordInputRef = ref();
const labelInputRef = ref();

const uploadRef = ref();

// 效验规则
const rules = {
	name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
	projectKeyword: [{ required: true, message: '请输入关键字', trigger: 'blur' }],
	targetLabel: [{ required: true, message: '请输入标签名', trigger: 'blur' }],
	tagScore: [{ required: true, message: '请输入单任务标注积分', trigger: 'blur' }],
	auditScore: [{ required: true, message: '请输入单任务审核积分', trigger: 'blur' }],
	coverImg: [{ required: true, message: '请上传样例图片', trigger: 'blur' }],
};

// 显示关键字输入框
const showKeywordInput = () => {
	if (keywordTags.value.length >= 3) {
		ElMessage.warning('最多只能添加3个关键字');
		return;
	}
	keywordInputVisible.value = true;
	nextTick(() => {
		keywordInputRef.value?.input?.focus();
	});
};

// 显示标签输入框
const showLabelInput = () => {
	labelInputVisible.value = true;
	nextTick(() => {
		labelInputRef.value?.input?.focus();
	});
};

// 确认关键字输入
const handleKeywordConfirm = () => {
	const value = keywordInputValue.value.trim();
	if (value && !keywordTags.value.includes(value) && keywordTags.value.length < 3) {
		keywordTags.value.push(value);
		form.value.projectKeyword = keywordTags.value.join(',');
	}
	keywordInputVisible.value = false;
	keywordInputValue.value = '';
};

// 确认标签输入
const handleLabelConfirm = () => {
	const value = labelInputValue.value.trim();
	if (value && !labelTags.value.includes(value)) {
		labelTags.value.push(value);
		form.value.targetLabel = labelTags.value.join(',');
	}
	labelInputVisible.value = false;
	labelInputValue.value = '';
};

// 删除关键字
const handleKeywordClose = (tag) => {
	keywordTags.value = keywordTags.value.filter((t) => t !== tag);
	form.value.projectKeyword = keywordTags.value.join(',');
};

// 删除标签
const handleLabelClose = (tag) => {
	labelTags.value = labelTags.value.filter((t) => t !== tag);
	form.value.targetLabel = labelTags.value.join(',');
};

// 获取详情
const getDetails = (id) => {
	formLoading.value = true;
	getProject(id).then((res) => {
		formLoading.value = false;
		form.value = Object.assign({}, form.value, res);
		// 初始化标签数组
		if (res.projectKeyword) {
			keywordTags.value = res.projectKeyword.split(',');
		}
		if (res.targetLabel) {
			labelTags.value = res.targetLabel.split(',');
		}
	});
};

// 弹框数据
const dialogData = reactive({
	isShow: false,
	title: '新增',
	id: null,
});

// 打开弹框
const openDialog = async (row) => {
	dialogData.isShow = true;
	dialogData.title = '新增项目';
	if (row?.id) {
		dialogData.id = row.id;
		dialogData.title = '编辑项目';
		form.value = {};
		getDetails(row.id);
	} else {
		form.value = cloneDeep(addDefaultForm);
	}
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
	dialogData.id = null;
	keywordInputValue.value = ''
	labelInputValue.value = ''
	keywordTags.value = []
	labelTags.value = []
	formRef.value.resetFields();
};

// 提交
const submit = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid) => {
		if (valid) {
			if (dialogData.id) {
				updateProjectApi({ ...form.value, projectType: props.projectType }).then(() => {
					ElMessage.success('操作成功');
					closeDialog();
					emits('refresh');
				});
			} else {
				addProjectApi({ ...form.value, projectType: props.projectType }).then(() => {
					ElMessage.success('操作成功');
					closeDialog();
					emits('refresh');
				});
			}
		}
	});
};

// 上传前校验
const beforeUpload = (file) => {
	const isImage = file.type.startsWith('image/');
	if (!isImage) {
		ElMessage.error('只能上传图片文件！');
		return false;
	}
	return true;
};

// 上传成功回调
const handleUploadSuccess = (response) => {
	if (response.code === 200 && response.data) {
		ElMessage.success('上传成功');
		form.value.coverImg = response.data.url;
	} else {
		// 当code不为0时，显示接口返回的错误信息
		ElMessage.error(response.msg || '上传失败');
		// 清除已上传的文件
		uploadRef.value?.clearFiles();
	}
};

// 上传失败回调
const handleUploadError = (error) => {
	console.error('Upload error:', error);
	ElMessage.error('上传失败，请稍后重试');
	// 清除已上传的文件
	uploadRef.value?.clearFiles();
};

// 超出文件数量限制
const handleExceed = () => {
	ElMessage.warning('只能上传一张图片');
};

defineExpose({
	openDialog,
});
</script>

<style scoped>
.tag-input {
	width: 100px;
	margin-left: 0;
	vertical-align: bottom;
}

.button-new-tag {
	margin-left: 0;
	padding-top: 0;
	padding-bottom: 0;
}
</style>
