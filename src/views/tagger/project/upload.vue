<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" :before-close="closeDialog" draggable :close-on-click-modal="false" :close-on-press-escape="false" width="700px">
			<div class="info-section" v-loading="formLoading">
				<div class="info-title">基础信息</div>
				<div class="info-content">
					<el-descriptions :column="2" border>
						<el-descriptions-item :span="2" label="数据集ID">{{ form.id || '--' }}</el-descriptions-item>
						<el-descriptions-item :span="2" label="数据集名称">{{ form.name || '--' }}</el-descriptions-item>
						<el-descriptions-item :span="2" label="关键字">
							<div class="tag-container">
								<el-tag v-for="tag in keywordTags" :key="tag" size="small">{{ tag }}</el-tag>
							</div>
						</el-descriptions-item>
						<el-descriptions-item :span="2" label="标签名">
							<div class="tag-container">
								<el-tag v-for="tag in labelTags" :key="tag" size="small">{{ tag }}</el-tag>
							</div>
						</el-descriptions-item>
						<el-descriptions-item label="单任务标注积分" :span="projectType === 2 ? 1 : 2">{{ form.tagScore || '0' }} TAG</el-descriptions-item>
						<el-descriptions-item v-if="projectType === 2" label="单任务审核积分">{{ form.auditScore || '0' }} TAG</el-descriptions-item>
						<el-descriptions-item label="本地样例图片">
							<el-image v-if="form.coverImg" :src="form.coverImg" style="width: 100px; height: 100px" />
							<div v-else class="image-placeholder">
								<el-icon><Picture /></el-icon>
							</div>
						</el-descriptions-item>
					</el-descriptions>
				</div>
			</div>

			<el-form ref="formRef" :model="form" label-width="140px" class="upload-section">
				<el-form-item label="上传原始数据集" required>
					<el-upload ref="uploadRef" v-model:file-list="fileList" :action="projectType === 2 ? '/dm/admin/projectHMUploadImagePack' : '/dm/admin/projectAIUploadImagePack'" :show-file-list="false" :headers="{ Authorization: getToken() }" :data="{ projectId: form.id }" :limit="1" :before-upload="beforeUpload" :on-success="handleUploadSuccess" :on-error="handleUploadError" :on-exceed="handleExceed" accept=".zip">
						<el-button type="primary" :loading="fileUploading" :disabled="formLoading">选择文件</el-button>
						<template #tip>
							<div class="el-upload__tip">共识别{{ form.validImgNum || 0 }}张，仅支持 .zip 格式文件</div>
						</template>
					</el-upload>
				</el-form-item>
			</el-form>

			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" v-debounce="submit" :loading="formSubmitting" :disabled="!form.validImgNum || formSubmitting">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { getProjectApi, updateProjectApi } from '@/api/tagger/project';
import { confirmManualProjectDataApi, confirmAIProjectDataApi } from '@/api/tagger/dm';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import { getToken } from '@/utils/token.js';
import { Picture } from '@element-plus/icons-vue';

const props = defineProps({ projectType: Number });
const uploadRef = ref();
const formRef = ref();
const emits = defineEmits(['refresh']);

// 关键字和标签数组
const keywordTags = ref([]);
const labelTags = ref([]);

// 表单
const form = ref({});
const fileList = ref([]);
const fileUploading = ref(false);
const formLoading = ref(false);
const formSubmitting = ref(false);

// 弹框数据
const dialogData = reactive({
	isShow: false,
	title: '上传数据集',
	id: null,
});

// 获取详情
const getDetails = (id) => {
	formLoading.value = true;
	getProjectApi(id).then((res) => {
		formLoading.value = false;
		form.value = res;
		// 初始化标签数组
		if (res.projectKeyword) {
			keywordTags.value = res.projectKeyword.split(',').filter((item) => !!item);
		}
		if (res.targetLabel) {
			labelTags.value = res.targetLabel.split(',').filter((item) => !!item);
		}
	});
};

// 上传前校验
const beforeUpload = (file) => {
	if (!form.value.id) {
		ElMessage.warning('数据集ID不能为空');
		return false;
	}

	const isZip = file.type === 'application/zip' || file.name.endsWith('.zip');
	if (!isZip) {
		ElMessage.error('只能上传 .zip 格式的文件！');
		return false;
	}
	fileUploading.value = true;
	return true;
};

// 上传成功回调
const handleUploadSuccess = (response) => {
	fileUploading.value = false;
	if (response.code === 0 && response.data) {
		ElMessage.success('上传成功');
		// 保存上传后的数据
		form.value = {
			...form.value,
			validImgNum: response.data.valid,
			fileName: response.data.fileName,
		};
	} else {
		// 当code不为0时，显示接口返回的错误信息
		ElMessage.error(response.msg || '上传失败');
		// 清除已上传的文件
		uploadRef.value?.clearFiles();
	}
};

// 上传失败回调
const handleUploadError = (error) => {
	fileUploading.value = false;
	console.error('Upload error:', error);
	ElMessage.error('上传失败，请稍后重试');
	// 清除已上传的文件
	uploadRef.value?.clearFiles();
};

// 超出文件数量限制
const handleExceed = () => {
	ElMessage.warning('只能上传一个文件');
};

// 打开弹框
const openDialog = (row) => {
	if (!row?.id) {
		ElMessage.warning('请先选择数据集');
		return;
	}
	dialogData.isShow = true;
	dialogData.id = row.id;
	form.value = {};
	getDetails(row.id);
};

// 关闭弹框
const closeDialog = (doneFn) => {
	if (formSubmitting.value) return;

	dialogData.isShow = false;
	dialogData.id = null;
	form.value = {};
	keywordTags.value = [];
	labelTags.value = [];
	fileList.value = [];
	doneFn();
};

// 提交
const submit = async () => {
	if (!form.value.validImgNum) {
		ElMessage.warning('请先上传数据集');
		return;
	}

	if (!fileList.value.length) {
		ElMessage.warning('请选择要上传的文件');
		return;
	}

	const formData = {
		projectId: form.value.id.toString(),
		fileName: form.value.fileName,
	};

	formSubmitting.value = true;

	const promise = props.projectType === 1 ? confirmAIProjectDataApi(formData) : confirmManualProjectDataApi(formData);

	promise
		.then(() => {
			ElMessage.success('上传成功');
			formSubmitting.value = false;
			emits('refresh');
			closeDialog();
		})
		.catch(() => {
			formSubmitting.value = false;
		});
};

defineExpose({
	openDialog,
});
</script>

<style scoped>
.info-section {
	margin-bottom: 24px;
}

.info-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 16px;
}

.info-content {
	padding: 16px;
	background-color: #f5f7fa;
	border-radius: 4px;
}

.tag-container {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;
}

.image-placeholder {
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #f5f7fa;
	border: 1px dashed #dcdfe6;
	border-radius: 4px;
}

.upload-section {
	margin-top: 24px;
}

.el-upload__tip {
	color: #999;
	font-size: 12px;
	margin-top: 8px;
}
</style>
