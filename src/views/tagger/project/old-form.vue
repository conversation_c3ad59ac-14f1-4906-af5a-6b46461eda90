<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="45%">
			<el-form ref="formRef" :rules="rules" :model="form" label-width="100px" v-loading="formLoading">
				<el-row :gutter="10">
					<el-col :span="24">
						<el-form-item label="数据集名称" prop="name">
							<el-input v-model="form.name" :maxlength="100" clearable placeholder="请输入数据集名称" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="项目类型" prop="projectType">
							<el-select v-model="form.projectType" clearable placeholder="请选择项目类型">
								<el-option label="AI辅助标注数据集" :value="1" />
								<el-option label="人工标注数据集" :value="2" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="项目关键字" prop="projectKeyword">
							<el-input v-model="form.projectKeyword" :maxlength="200" clearable placeholder="请输入项目关键字" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="单任务标注积分" prop="tagScore">
							<el-input-number v-model="form.tagScore" placeholder="请输入单任务标注积分" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="单任务审核积分" prop="auditScore">
							<el-input-number v-model="form.auditScore" placeholder="请输入单任务审核积分" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="封面图片" prop="coverImg">
							<el-input v-model="form.coverImg" :maxlength="200" clearable placeholder="请输入封面图片" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="项目状态" prop="projectStatus">
							<el-select v-model="form.projectStatus" clearable placeholder="请选择项目状态">
								<el-option label="待上传" :value="1" />
								<el-option label="待重新上传" :value="2" />
								<el-option label="加密中" :value="3" />
								<el-option label="开放中" :value="4" />
								<el-option label="已关闭" :value="5" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="图片总量" prop="totalImgNum">
							<el-input-number v-model="form.totalImgNum" placeholder="请输入图片总量" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="已标注量" prop="markedNum">
							<el-input-number v-model="form.markedNum" placeholder="请输入已标注量" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始数据集上传gf状态" prop="originalGfStatus">
							<el-select v-model="form.originalGfStatus" clearable placeholder="请选择原始数据集上传gf状态">
								<el-option label="待上传" :value="1" />
								<el-option label="上传中" :value="2" />
								<el-option label="已上传" :value="3" />
								<el-option label="待重新上传" :value="4" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签文件上传gf状态" prop="tagGfStatus">
							<el-select v-model="form.tagGfStatus" clearable placeholder="请选择标签文件上传gf状态">
								<el-option label="待上传" :value="1" />
								<el-option label="上传中" :value="2" />
								<el-option label="已上传" :value="3" />
								<el-option label="待重新上传" :value="4" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始索引mintNFT状态" prop="originalMintStatus">
							<el-select v-model="form.originalMintStatus" clearable placeholder="请选择原始索引mintNFT状态">
								<el-option label="已mint" :value="1" />
								<el-option label="未mint" :value="0" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始索引mintNFT地址" prop="originalMintPath">
							<el-input v-model="form.originalMintPath" :maxlength="300" clearable placeholder="请输入原始索引mintNFT地址" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始索引mintNFT钱包地址" prop="originalMintWallet">
							<el-input v-model="form.originalMintWallet" :maxlength="200" clearable placeholder="请输入原始索引mintNFT钱包地址" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始索引mintNFT私钥" prop="originalMintKey">
							<el-input v-model="form.originalMintKey" :maxlength="200" clearable placeholder="请输入原始索引mintNFT私钥" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始索引mintNFT时间" prop="originalMintTime">
							<date-picker type="datetime" v-model="form.originalMintTime" clearable placeholder="请选择原始索引mintNFT时间" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始索引mintNFT用户ID" prop="originalMintUserId">
							<el-input-number v-model="form.originalMintUserId" placeholder="请输入原始索引mintNFT用户ID" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="原始索引mintNFT结果" prop="originalMintResult">
							<el-input v-model="form.originalMintResult" :maxlength="1000" clearable placeholder="请输入原始索引mintNFT结果" type="textarea" :autosize="{ minRows: 2 }" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签索引mintNFT状态" prop="tagMintStatus">
							<el-select v-model="form.tagMintStatus" clearable placeholder="请选择标签索引mintNFT状态">
								<el-option label="已mint" :value="1" />
								<el-option label="未mint" :value="0" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签索引mintNFT地址" prop="tagMintPath">
							<el-input v-model="form.tagMintPath" :maxlength="300" clearable placeholder="请输入标签索引mintNFT地址" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签索引mintNFT钱包地址" prop="tagMintWallet">
							<el-input v-model="form.tagMintWallet" :maxlength="200" clearable placeholder="请输入标签索引mintNFT钱包地址" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签索引mintNFT私钥" prop="tagMintKey">
							<el-input v-model="form.tagMintKey" :maxlength="200" clearable placeholder="请输入标签索引mintNFT私钥" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签索引mintNFT时间" prop="tagMintTime">
							<date-picker type="datetime" v-model="form.tagMintTime" clearable placeholder="请选择标签索引mintNFT时间" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签索引mintNFT用户ID" prop="tagMintUserId">
							<el-input-number v-model="form.tagMintUserId" placeholder="请输入标签索引mintNFT用户ID" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="标签索引mintNFT结果" prop="tagMintResult">
							<el-input v-model="form.tagMintResult" :maxlength="1000" clearable placeholder="请输入标签索引mintNFT结果" type="textarea" :autosize="{ minRows: 2 }" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="已通过审核量" prop="approvedNum">
							<el-input-number v-model="form.approvedNum" placeholder="请输入已通过审核量" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="单图需标注目标类型数量" prop="signImgTargetNum">
							<el-input-number v-model="form.signImgTargetNum" placeholder="请输入单图需标注目标类型数量" :controls="false" :min="0" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="需标注目标类型标签名" prop="targetLabel">
							<el-input v-model="form.targetLabel" :maxlength="200" clearable placeholder="请输入需标注目标类型标签名" />
						</el-form-item>
					</el-col>
					<el-col :span="24">
						<el-form-item label="备注" prop="remark">
							<el-input v-model="form.remark" :maxlength="200" clearable placeholder="请输入备注" />
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" v-debounce="submit">确定</el-button>
					<el-button @click="closeDialog">取消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { addProjectApi, getProjectApi, updateProjectApi } from '@/api/tagger/project';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';

const formRef = ref();

const emits = defineEmits(['refresh']);

// 表单
let addDefaultForm = {};
let form = ref({});

let formLoading = ref(false);

// 效验规则
const rules = {
	name: [{ required: true, message: '请输入数据集名称', trigger: 'blur' }],
	projectType: [{ required: true, message: '请选择项目类型', trigger: 'change' }],
	tagScore: [{ required: true, message: '请输入单任务标注积分', trigger: 'blur' }],
	coverImg: [{ required: true, message: '请输入封面图片', trigger: 'blur' }],
	projectStatus: [{ required: true, message: '请选择项目状态', trigger: 'change' }],
	totalImgNum: [{ required: true, message: '请输入图片总量', trigger: 'blur' }],
	markedNum: [{ required: true, message: '请输入已标注量', trigger: 'blur' }],
	originalGfStatus: [{ required: true, message: '请选择原始数据集上传gf状态', trigger: 'change' }],
	tagGfStatus: [{ required: true, message: '请选择标签文件上传gf状态', trigger: 'change' }],
	originalMintStatus: [{ required: true, message: '请选择原始索引mintNFT状态', trigger: 'change' }],
	originalMintPath: [{ required: true, message: '请输入原始索引mintNFT地址', trigger: 'blur' }],
	tagMintStatus: [{ required: true, message: '请选择标签索引mintNFT状态', trigger: 'change' }],
	tagMintPath: [{ required: true, message: '请输入标签索引mintNFT地址', trigger: 'blur' }],
};

// 获取详情
const getDetails = (id) => {
	formLoading.value = true;
	getProjectApi(id).then((res) => {
		formLoading.value = false;
		form.value = Object.assign({}, form.value, res);
	});
};

// 弹框数据
const dialogData = reactive({
	isShow: false,
	title: '新增',
	id: null,
});

// 打开弹框
const openDialog = async (row) => {
	dialogData.isShow = true;
	dialogData.title = '新增项目';
	if (row?.id) {
		dialogData.id = row.id;
		dialogData.title = '编辑项目';
		form.value = {};
		getDetails(row.id);
	} else {
		form.value = addDefaultForm;
	}
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
	dialogData.id = null;
	formRef.value.resetFields();
};

// 提交
const submit = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid) => {
		if (valid) {
			if (dialogData.id) {
				updateProjectApi(form.value).then(() => {
					ElMessage.success('操作成功');
					closeDialog();
					emits('refresh');
				});
			} else {
				addProjectApi(form.value).then(() => {
					ElMessage.success('操作成功');
					closeDialog();
					emits('refresh');
				});
			}
		}
	});
};

defineExpose({
	openDialog,
});
</script>
