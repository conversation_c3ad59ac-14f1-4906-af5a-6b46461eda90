<template>
	<el-card shadow="never" class="card-box">
		<el-form :model="queryForm" label-width="160px">
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="项目状态">
						<el-select v-model="queryForm.projectStatus" clearable placeholder="请选择项目状态">
							<el-option v-for="item in projectStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="原始数据集上传gf状态">
						<el-select v-model="queryForm.originalGfStatus" clearable placeholder="请选择原始数据集上传gf状态">
							<el-option v-for="item in gfStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="标签文件上传gf状态">
						<el-select v-model="queryForm.tagGfStatus" clearable placeholder="请选择标签文件上传gf状态">
							<el-option v-for="item in gfStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="原始索引mintNFT状态">
						<el-select v-model="queryForm.originalMintStatus" clearable placeholder="请选择原始索引mintNFT状态">
							<el-option label="已mint" :value="1" />
							<el-option label="未mint" :value="0" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="标签索引mintNFT状态">
						<el-select v-model="queryForm.tagMintStatus" clearable placeholder="请选择标签索引mintNFT状态">
							<el-option label="已mint" :value="1" />
							<el-option label="未mint" :value="0" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :lg="8" :md="12" :sm="24" :xl="6">
					<el-form-item label="创建时间">
						<date-picker type="daterange" v-model:startDate="queryForm.createTimeStart" v-model:endDate="queryForm.createTimeEnd" clearable start-placeholder="开始时间" end-placeholder="结束时间" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="搜索">
						<el-input v-model="queryForm.keyword" @keyup.enter="onSearch" clearable placeholder="请输入名称" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label-width="0">
						<el-button type="primary" @click="onSearch">
							<el-icon>
								<ele-search />
							</el-icon>
							<span class="search-btn__left">查询</span>
						</el-button>
						<el-button @click="onReset">
							<el-icon>
								<ele-refresh />
							</el-icon>
							<span class="search-btn__left">重置</span>
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class="table-btn-box mb-3">
			<el-button v-auth="'t:project:add'" type="primary" @click="openDialog">
				<el-icon class="mr-1">
					<ele-circle-plus />
				</el-icon>
				新增
			</el-button>
		</div>
		<el-table v-loading="tableData.isLoading" :data="tableData.data" border>
			<el-table-column type="index" label="序号" width="60" align="center" />
			<el-table-column prop="id" label="数据集ID" align="center" width="200" />
			<el-table-column prop="name" label="名称" align="center" />
			<el-table-column prop="projectStatus" label="数据集状态" align="center">
				<template #default="scope">
					{{ getLabel(projectStatusOptions, scope.row.projectStatus) }}
				</template>
			</el-table-column>
			<el-table-column prop="createTime" label="创建时间" align="center"  width="200" />
			<el-table-column prop="totalImgNum" label="图片总量" align="center" />
			<el-table-column prop="markedNum" label="已完成标注量" align="center" />
			<el-table-column prop="tagScore" label="单任务标注积分" align="center">
				<template #default="scope"> {{ scope.row.tagScore }} TAG </template>
			</el-table-column>
			<el-table-column v-if="queryForm.projectType === 2" prop="approvedNum" label="已通过审核量" align="center" />
			<el-table-column v-if="queryForm.projectType === 2" prop="auditScore" label="单任务审核积分" align="center" />
			<el-table-column v-if="queryForm.projectType === 2" prop="markedNum" label="单图需标注目标类型数量" align="center">
				<template #default="scope"> {{ (scope.row.targetLabel || '').split(',').filter((item) => !!item).length }} </template>
			</el-table-column>
			<el-table-column label="原始数据集" align="center">
				<el-table-column label="上传gf" align="center" width="150">
					<template #default="scope">
						<div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
							<span>{{ getLabel(gfStatusOptions, scope.row.originalGfStatus) }}</span>
							<el-button
								v-if="scope.row.originalGfStatus !== 3"
								link
								type="primary"
								size="small"
								:loading="scope.row.uploadLoading"
								@click="uploadOriginalToGreenField(scope.row)"
								title="上传原始图片索引到greenfield"
							>
								<el-icon><ele-upload /></el-icon>
							</el-button>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="mintNFT" align="center" width="120">
					<template #default="scope">
						<span v-if="scope.row.originalMintStatus === 1">已mint</span>
						<el-button v-else link type="primary" :disabled="scope.row.originalGfStatus !== 3" @click="openMintDialog(scope.row, 'original')">Mint NFT</el-button>
					</template>
				</el-table-column>
			</el-table-column>
			<el-table-column label="标签文件" align="center">
				<el-table-column label="上传gf" align="center" width="150">
					<template #default="scope">
						<div style="display: flex; align-items: center; justify-content: center; gap: 8px;">
							<span>{{ getLabel(gfStatusOptions, scope.row.tagGfStatus) }}</span>
							<el-button
								v-if="scope.row.tagGfStatus !== 3"
								link
								type="primary"
								size="small"
								:loading="scope.row.uploadLoading"
								@click="uploadTagToGreenField(scope.row)"
								title="上传标签文件索引到greenfield"
							>
								<el-icon><ele-upload /></el-icon>
							</el-button>
						</div>
					</template>
				</el-table-column>
				<el-table-column label="mintNFT" align="center"	width="120">
					<template #default="scope">
						<span v-if="scope.row.tagMintStatus === 1">已mint</span>
						<el-button v-else link type="primary" :disabled="!scope.row.tagMintExternalUrl" @click="openMintDialog(scope.row, 'tag')">Mint NFT</el-button>
					</template>
				</el-table-column>
			</el-table-column>
			<el-table-column label="操作" fixed="right" align="center" width="200">
				<template #default="{ row }">
					<el-button link type="primary" @click="openDetailDialog(row)">查看</el-button>
					<el-button v-if="row.projectStatus === 1 || row.projectStatus === 2" link type="primary" @click="openUploadDialog(row)">上传</el-button>
					<el-button v-auth="'t:project:delete'" link type="danger" @click="delTable(row)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>

		<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

		<TableForm ref="tableDialogRef" :project-type="queryForm.projectType" @refresh="getTableList" />
		<UploadForm ref="uploadDialogRef" :project-type="queryForm.projectType" @refresh="getTableList" />
		<DetailDialog ref="detailDialogRef" :project-type="queryForm.projectType" @refresh="getTableList" />
		<MintDialog ref="mintDialogRef" type="original" @refresh="getTableList" />
	</el-card>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { deleteProjectApi, getProjectPageApi, uploadOriginalToGreenfieldApi, uploadTagToGreenfieldApi } from '@/api/tagger/project';
import TableForm from './form.vue';
import UploadForm from './upload.vue';
import DetailDialog from './detail.vue';
import MintDialog from './mint.vue';
import { reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getLabel, gfStatusOptions, projectStatusOptions } from './utils';

const route = useRoute();
let queryForm = ref({});

// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {
		projectType: route.path.endsWith('ai') ? 1 : 2,
	};
	pageData.pageNum = 1;
	getTableList();
};

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

// 排序
const orderBy = ref({});
const sortChange = ({ prop, order }) => {
	if (order) {
		orderBy.value.orderByColumn = prop;
		orderBy.value.orderByAsc = order === 'ascending';
	} else {
		orderBy.value = {};
	}
	pageData.pageNum = 1;
	getTableList();
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 获取表格列表
const getTableList = () => {
	tableData.isLoading = true;
	console.log(queryForm.value.projectType);
	getProjectPageApi({ ...pageData, ...queryForm.value, ...orderBy.value }).then((res) => {
		tableData.data = res?.list || [];
		pageData.total = res?.total || 0;
		tableData.isLoading = false;
	});
};

// 删除列表数据
function delTable(row) {
	ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deleteProjectApi(row.id).then(() => {
				ElMessage.success('删除成功');
				getTableList();
			});
		})
		.catch(() => {});
}

// 新增、编辑弹框
const tableDialogRef = ref(null);
const uploadDialogRef = ref(null);
const detailDialogRef = ref(null);
const mintDialogRef = ref();
// 打开表格操作弹框
function openDialog(data = {}) {
	tableDialogRef.value.openDialog(data);
}

function openUploadDialog(data = {}) {
	uploadDialogRef.value.openDialog(data);
}

function openDetailDialog(data = {}) {
	detailDialogRef.value.openDialog(data);
}

function openMintDialog(data = {}, type) {
	mintDialogRef.value.openDialog(data, type);
}

// 上传greenfield数据
const uploadTagToGreenField = async (row) => {
	console.log('test');
	try {
		row.uploadLoading = true;
		await uploadTagToGreenfieldApi(row.id);
		ElMessage.success('上传成功');
		getTableList();
	} catch (error) {
		console.error('上传失败:', error);
	} finally {
		row.uploadLoading = false;
	}
};
// 上传greenfield数据
const uploadOriginalToGreenField = async (row) => {
	console.log('test');
	try {
		row.uploadLoading = true;
		await uploadOriginalToGreenfieldApi(row.id);
		ElMessage.success('上传成功');
		getTableList();
	} catch (error) {
		console.error('上传失败:', error);
	} finally {
		row.uploadLoading = false;
	}
};

watch(
	route,
	(val) => {
		queryForm.value.projectType = val.path.endsWith('ai') ? 1 : 2;
		getTableList();
	},
	{ immediate: true }
);
</script>

<style scoped>
.el-table :deep(.el-table__header) th {
	background-color: #f5f7fa;
	color: #606266;
}

.el-table :deep(.el-radio__label) {
	font-size: 12px;
}
</style>
