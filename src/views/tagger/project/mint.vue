<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close title="mint NFT" :before-close="closeDialog" draggable :close-on-click-modal="false" :close-on-press-escape="false" width="500px">
			<div v-loading="formLoading">
				<el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
					<el-form-item label="钱包地址" prop="mintWallet">
						<div class="address-input">
							<el-input v-model="form.mintWallet" :disabled="!isEditing" placeholder="请输入钱包地址" />
							<el-button type="primary" link @click="handleEdit">
								{{ isEditing ? '确认修改' : '修改地址' }}
							</el-button>
						</div>
					</el-form-item>
					<el-form-item label="私钥" prop="mintKey">
						<el-input v-model="form.mintKey" :disabled="!isEditing" type="password" show-password placeholder="请输入私钥" />
					</el-form-item>
				</el-form>
			</div>

			<template #footer>
				<div class="dialog-footer">
					<el-button type="primary" :loading="formSubmitting" :disabled="formSubmitting" @click="submit">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { getLastMintNFTApi, mintNFTApi } from '@/api/tagger/project';
import { reactive, ref } from 'vue';

const props = defineProps({
	type: {
		type: String,
		required: true,
		validator: (value) => ['original', 'tag'].includes(value),
	},
});
const emits = defineEmits(['refresh']);

// 表单ref
const formRef = ref();
const formLoading = ref(false);
const formSubmitting = ref(false);
const isEditing = ref(false);

// 表单数据
const form = ref({
	id: null,
	type: null,
	mintWallet: '',
	mintKey: '',
});

// 验证规则
const rules = {
	mintWallet: [{ required: true, message: '钱包地址不能为空', trigger: 'blur' }],
	mintKey: [{ required: true, message: '私钥不能为空', trigger: 'blur' }],
};

// 弹框数据
const dialogData = reactive({
	isShow: false,
	id: null,
});

// 获取最新的NFT钱包密钥
const getLastMintNFT = async () => {
	formLoading.value = true;
	try {
		const res = await getLastMintNFTApi();

		form.value.mintWallet = res ? res.mintWallet : undefined;
		form.value.mintKey = res ? res.mintKey : undefined;
		isEditing.value = !res;
	} catch (error) {
		console.error('Get mint NFT info error:', error);
		ElMessage.error('获取钱包信息失败');
	} finally {
		formLoading.value = false;
	}
};

const handleEdit = () => {
	if (isEditing.value) {
		// 确认修改
		isEditing.value = false;
		ElMessage.success('地址修改成功');
	} else {
		// 开始编辑
		isEditing.value = true;
	}
};

// 提交
const submit = async () => {
	if (!formRef.value) return;
	await formRef.value.validate((valid) => {
		if (valid) {
			formSubmitting.value = true;

			const submitData = {
				id: dialogData.id,
				type: form.value.type === 'original' ? 1 : 2,
				mintWallet: form.value.mintWallet,
				mintKey: form.value.mintKey,
			};

			mintNFTApi(submitData)
				.then(() => {
					ElMessage.success('mint成功');
					formSubmitting.value = false;
					emits('refresh');
					closeDialog();
				})
				.catch(() => {
					ElMessage.error('mint失败');
					formSubmitting.value = false;
				});
		}
	});
};

// 打开弹框
const openDialog = async (row, currType) => {
	if (!row?.id) {
		ElMessage.warning('请先选择数据集');
		return;
	}
	dialogData.isShow = true;
	dialogData.id = row.id;
	form.value.type = currType;
	await getLastMintNFT();
};

// 关闭弹框
const closeDialog = (doneFn) => {
	if (formSubmitting.value) return;
	dialogData.isShow = false;
	dialogData.id = null;
	form.value = {
		id: null,
		type: null,
		mintWallet: '',
		mintKey: '',
	};
	isEditing.value = false;
	if (doneFn) doneFn();
};

defineExpose({
	openDialog,
});
</script>

<style scoped>
.address-input {
	display: flex;
	gap: 8px;
	align-items: center;
}

.address-input .el-input {
	flex: 1;
}
</style>
