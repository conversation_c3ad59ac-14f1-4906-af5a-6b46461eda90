<template>
	<el-card shadow="never" class="card-box">
		<el-form :model="queryForm" label-width="100px">
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="项目类型">
						<el-select v-model="queryForm.projectType" clearable placeholder="请选择项目类型">
							<el-option label="AI辅助标注数据集" :value="1" />
							<el-option label="人工标注数据集" :value="2" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="项目状态">
						<el-select v-model="queryForm.projectStatus" clearable placeholder="请选择项目状态">
							<el-option label="待上传" :value="1" />
							<el-option label="待重新上传" :value="2" />
							<el-option label="加密中" :value="3" />
							<el-option label="开放中" :value="4" />
							<el-option label="已关闭" :value="5" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="原始数据集上传gf状态">
						<el-select v-model="queryForm.originalGfStatus" clearable placeholder="请选择原始数据集上传gf状态">
							<el-option label="待上传" :value="1" />
							<el-option label="上传中" :value="2" />
							<el-option label="已上传" :value="3" />
							<el-option label="待重新上传" :value="4" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="标签文件上传gf状态">
						<el-select v-model="queryForm.tagGfStatus" clearable placeholder="请选择标签文件上传gf状态">
							<el-option label="待上传" :value="1" />
							<el-option label="上传中" :value="2" />
							<el-option label="已上传" :value="3" />
							<el-option label="待重新上传" :value="4" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="原始索引mintNFT状态">
						<el-select v-model="queryForm.originalMintStatus" clearable placeholder="请选择原始索引mintNFT状态">
							<el-option label="已mint" :value="1" />
							<el-option label="未mint" :value="0" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="标签索引mintNFT状态">
						<el-select v-model="queryForm.tagMintStatus" clearable placeholder="请选择标签索引mintNFT状态">
							<el-option label="已mint" :value="1" />
							<el-option label="未mint" :value="0" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :lg="8" :md="12" :sm="24" :xl="6">
					<el-form-item label="创建时间">
						<date-picker type="daterange" v-model:startDate="queryForm.createTimeStart" v-model:endDate="queryForm.createTimeEnd" clearable start-placeholder="开始时间" end-placeholder="结束时间" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="搜索">
						<el-input v-model="queryForm.keyword" @keyup.enter="onSearch" clearable placeholder="请输入名称" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label-width="0">
						<el-button type="primary" @click="onSearch">
							<el-icon>
								<ele-search />
							</el-icon>
							<span class="search-btn__left">查询</span>
						</el-button>
						<el-button @click="onReset">
							<el-icon>
								<ele-refresh />
							</el-icon>
							<span class="search-btn__left">重置</span>
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class="table-btn-box mb-3">
			<el-button v-auth="'t:project:add'" type="primary" @click="openDialog">
				<el-icon class="mr-1">
					<ele-circle-plus />
				</el-icon>
				新增
			</el-button>
		</div>
		<el-table v-loading="tableData.isLoading" :data="tableData.data" @sort-change="sortChange" border row-key="id">
			<el-table-column prop="number" label="序号" align="center" width="60px" />
			<el-table-column prop="name" label="数据集名称" align="center" />
			<el-table-column prop="projectType" label="项目类型" align="center">
				<template #default="scope">
					<span v-if="scope.row.projectType == 1">AI辅助标注数据集</span>
					<span v-if="scope.row.projectType == 2">人工标注数据集</span>
				</template>
			</el-table-column>
			<el-table-column prop="projectKeyword" label="项目关键字" align="center" />
			<el-table-column prop="tagScore" label="单任务标注积分" align="center" />
			<el-table-column prop="auditScore" label="单任务审核积分" align="center" />
			<el-table-column prop="coverImg" label="封面图片" align="center" />
			<el-table-column prop="projectStatus" label="项目状态" align="center">
				<template #default="scope">
					<span v-if="scope.row.projectStatus == 1">待上传</span>
					<span v-if="scope.row.projectStatus == 2">待重新上传</span>
					<span v-if="scope.row.projectStatus == 3">加密中</span>
					<span v-if="scope.row.projectStatus == 4">开放中</span>
					<span v-if="scope.row.projectStatus == 5">已关闭</span>
				</template>
			</el-table-column>
			<el-table-column prop="totalImgNum" label="图片总量" align="center" />
			<el-table-column prop="markedNum" label="已标注量" align="center" />
			<el-table-column prop="originalGfStatus" label="原始数据集上传gf状态" align="center">
				<template #default="scope">
					<span v-if="scope.row.originalGfStatus == 1">待上传</span>
					<span v-if="scope.row.originalGfStatus == 2">上传中</span>
					<span v-if="scope.row.originalGfStatus == 3">已上传</span>
					<span v-if="scope.row.originalGfStatus == 4">待重新上传</span>
				</template>
			</el-table-column>
			<el-table-column prop="tagGfStatus" label="标签文件上传gf状态" align="center">
				<template #default="scope">
					<span v-if="scope.row.tagGfStatus == 1">待上传</span>
					<span v-if="scope.row.tagGfStatus == 2">上传中</span>
					<span v-if="scope.row.tagGfStatus == 3">已上传</span>
					<span v-if="scope.row.tagGfStatus == 4">待重新上传</span>
				</template>
			</el-table-column>
			<el-table-column prop="originalMintStatus" label="原始索引mintNFT状态" align="center">
				<template #default="scope">
					<span v-if="scope.row.originalMintStatus == 1">已mint</span>
					<span v-if="scope.row.originalMintStatus == 0">未mint</span>
				</template>
			</el-table-column>
			<el-table-column prop="originalMintPath" label="原始索引mintNFT地址" align="center" />
			<el-table-column prop="originalMintWallet" label="原始索引mintNFT钱包地址" align="center" />
			<el-table-column prop="originalMintKey" label="原始索引mintNFT私钥" align="center" />
			<el-table-column prop="originalMintUserId" label="原始索引mintNFT用户ID" align="center" />
			<el-table-column prop="originalMintResult" label="原始索引mintNFT结果" align="center" />
			<el-table-column prop="tagMintStatus" label="标签索引mintNFT状态" align="center">
				<template #default="scope">
					<span v-if="scope.row.tagMintStatus == 1">已mint</span>
					<span v-if="scope.row.tagMintStatus == 0">未mint</span>
				</template>
			</el-table-column>
			<el-table-column prop="tagMintPath" label="标签索引mintNFT地址" align="center" />
			<el-table-column prop="tagMintWallet" label="标签索引mintNFT钱包地址" align="center" />
			<el-table-column prop="tagMintKey" label="标签索引mintNFT私钥" align="center" />
			<el-table-column prop="tagMintUserId" label="标签索引mintNFT用户ID" align="center" />
			<el-table-column prop="tagMintResult" label="标签索引mintNFT结果" align="center" />
			<el-table-column prop="approvedNum" label="已通过审核量" align="center" />
			<el-table-column prop="signImgTargetNum" label="单图需标注目标类型数量" align="center" />
			<el-table-column prop="targetLabel" label="需标注目标类型标签名" align="center" />
			<el-table-column prop="remark" label="备注" align="center" />
			<el-table-column prop="createTime" label="创建时间" align="center" min-width="150" sortable="custom" />
			<el-table-column label="操作" fixed="right" align="center" min-width="120">
				<template #default="{ row }">
					<el-button link type="primary" @click="openDetailDialog(row)"> 查看 </el-button>
					<el-button link type="primary" @click="openUploadDialog(row)"> 上传 </el-button>
					<el-button v-auth="'t:project:delete'" link type="primary" @click="delTable(row)">
						<el-icon>
							<ele-delete />
						</el-icon>
						删除
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

		<OldTableForm ref="tableDialogRef" @refresh="getTableList" />
		<UploadForm ref="uploadDialogRef" @refresh="getTableList" />
		<DetailDialog ref="detailDialogRef" />
	</el-card>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { deleteProjectApi, getProjectPageApi } from '@/api/tagger/project';
import TableForm from './form.vue';
import OldTableForm from './old-form.vue';
import UploadForm from './upload.vue';
import DetailDialog from './detail.vue';
import { reactive, ref } from 'vue';

let queryForm = ref({});

// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {};
	pageData.pageNum = 1;
	getTableList();
};

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

// 排序
const orderBy = ref({});
const sortChange = ({ prop, order }) => {
	if (order) {
		orderBy.value.orderByColumn = prop;
		orderBy.value.orderByAsc = order === 'ascending';
	} else {
		orderBy.value = {};
	}
	pageData.pageNum = 1;
	getTableList();
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 获取表格列表
const getTableList = () => {
	tableData.isLoading = true;
	getProjectPageApi({ ...pageData, ...queryForm.value, ...orderBy.value }).then((res) => {
		tableData.data = res?.list?.map((p, i) => {
			p.number = 1 + i + (pageData.pageNum - 1) * pageData.pageSize;
			return p;
		});
		pageData.total = res?.total;
		tableData.isLoading = false;
	});
};

// 删除列表数据
function delTable(row) {
	ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deleteProjectApi(row.id).then(() => {
				ElMessage.success('删除成功');
				getTableList();
			});
		})
		.catch(() => {});
}

// 新增、编辑弹框
const tableDialogRef = ref(null);
const uploadDialogRef = ref(null);
const detailDialogRef = ref(null);
// 打开表格操作弹框
function openDialog(data = {}) {
	tableDialogRef.value.openDialog(data);
}

function openUploadDialog(data = {}) {
	uploadDialogRef.value.openDialog(data);
}

function openDetailDialog(data = {}) {
	detailDialogRef.value.openDialog(data);
}

getTableList();
</script>
