<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close title="查看数据集" @close="closeDialog" draggable width="900px">
			<div v-loading="formLoading">
				<!-- 基础信息 -->
				<div class="section">
					<div class="section-title">基础信息</div>
					<div class="section-content">
						<el-descriptions :column="2" border>
							<el-descriptions-item label="数据集ID">{{ form.id }}</el-descriptions-item>
							<el-descriptions-item label="名称">{{ form.name }}</el-descriptions-item>
							<el-descriptions-item label="创建时间">{{ form.createTime }}</el-descriptions-item>
							<el-descriptions-item label="数据集状态">
								<el-radio-group v-if="form.projectStatus === 4 || form.projectStatus === 5" v-model="form.projectStatus" size="small" :disabled="form.projectStatus === 5" @change="handleStatusChange">
									<el-radio :label="4">开放</el-radio>
									<el-radio :label="5">关闭</el-radio>
								</el-radio-group>
								<div v-else>{{ getLabel(projectStatusOptions, form.projectStatus) }}</div>
							</el-descriptions-item>
							<el-descriptions-item :span="2" label="关键字">
								<div class="tag-container">
									<el-tag v-for="tag in keywordTags" :key="tag" size="small">{{ tag }}</el-tag>
								</div>
							</el-descriptions-item>
							<el-descriptions-item v-if="projectType === 2" :span="2" label="标签名">
								<div class="tag-container">
									<el-tag v-for="tag in labelTags" :key="tag" size="small">{{ tag }}</el-tag>
								</div>
							</el-descriptions-item>
							<el-descriptions-item label="样例图片">
								<el-image v-if="form.coverImg" :src="form.coverImg" style="width: 100px; height: 100px" />
								<div v-else class="image-placeholder">
									<el-icon><Picture /></el-icon>
								</div>
							</el-descriptions-item>
						</el-descriptions>
					</div>
				</div>

				<!-- 关键信息 -->
				<div class="section">
					<div class="section-title">关键信息</div>
					<div class="section-content">
						<el-descriptions :column="3" border>
							<el-descriptions-item label="图片总量" :span="3">{{ form.totalImgNum }}</el-descriptions-item>
							<el-descriptions-item label="已完成标注量">{{ form.markedNum || 0 }}</el-descriptions-item>
							<el-descriptions-item label="单任务标注积分">{{ form.tagScore }} TAG</el-descriptions-item>
							<el-descriptions-item label="已发放标注积分">{{ form.totalTagScore }} TAG</el-descriptions-item>
							<el-descriptions-item v-if="projectType === 2" label="已通过审核量">{{ form.approvedNum || 0 }}</el-descriptions-item>
							<el-descriptions-item v-if="projectType === 2" label="单任务审核积分">{{ form.auditScore }} TAG</el-descriptions-item>
							<el-descriptions-item v-if="projectType === 2" label="已发放审核积分">{{ form.totalAuditScore }} TAG</el-descriptions-item>
							<el-descriptions-item label="原始数据集与对应索引" :span="3">
								<div class="sub-info">
									<div>上传greenfield：{{ getLabel(gfStatusOptions, form.originalGfStatus) }}</div>
									<div>上传原始索引时间：{{ form.uploadOriginalGfTime || '--' }}</div>
									<div>上传原始索引OSS Key：{{ form.uploadOriginalGfOssKey || '--' }}</div>
									<div>索引mintNFT：{{ form.originalMintStatus === 1 ? '已mint' : '未mint' }}</div>
									<div>完成mint时间：{{ form.originalMintTime || '--' }}</div>
									<div>费用：{{ form.originalMintFee || '--' }}</div>
								</div>
							</el-descriptions-item>
							<el-descriptions-item label="标签文件与对应索引" :span="3">
								<div class="sub-info">
									<div>上传greenfield：{{ getLabel(gfStatusOptions, form.tagGfStatus) }}</div>
									<div>上传标签索引时间：{{ form.uploadTagGfTime || '--' }}</div>
									<div>上传标签索引OSS Key：{{ form.uploadTagGfOssKey || '--' }}</div>
									<div>索引mintNFT：{{ form.tagMintStatus === 1 ? '已mint' : '未mint' }}</div>
									<div>完成mint时间：{{ form.tagMintTime || '--' }}</div>
									<div>费用：{{ form.tagMintFee || '--' }}</div>
								</div>
							</el-descriptions-item>
							<el-descriptions-item label="标签ZIP文件" :span="3">
								<div class="sub-info">
									<div>上传标签ZIP时间：{{ form.uploadTagZipGfTime || '--' }}</div>
									<div>上传标签ZIP路径：{{ form.uploadTagZipGfUrl || '--' }}</div>
									<div>上传标签ZIP OSS Key：{{ form.uploadTagZipGfOssKey || '--' }}</div>
								</div>
							</el-descriptions-item>
						</el-descriptions>
					</div>
				</div>

				<!-- 用户列表 -->
				<div class="section">
					<div class="section-title">用户列表</div>
					<div class="section-content">
						<el-table :data="userList" border style="width: 100%" v-loading="userListLoading">
							<el-table-column type="index" label="序号" width="80" align="center" />
							<el-table-column prop="walletAddress" label="用户地址" align="center" />
							<el-table-column prop="realFinishTagNum" label="实际完成标注量" align="center" />
							<el-table-column prop="scoreTagNum" label="得分标注量" align="center" />
							<el-table-column prop="tagScore" label="标注获得积分" align="center" />
							<el-table-column v-if="props.projectType === 2" prop="realFinishAuditNum" label="实际完成审核量" align="center" />
							<el-table-column v-if="props.projectType === 2" prop="scoreAuditNum" label="得分审核量" align="center" />
							<el-table-column v-if="props.projectType === 2" prop="auditScore" label="审核获得积分" align="center" />
							<el-table-column label="操作" width="120" align="center">
								<template #default="{ row }">
									<el-button link type="primary" @click="checkUser(row)">查看用户</el-button>
								</template>
							</el-table-column>
						</el-table>

						<!-- 分页 -->
						<div class="pagination-container">
							<el-pagination :current-page="queryParams.pageNum" :page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
						</div>
					</div>
				</div>
			</div>
		</el-dialog>
		
		<!-- 用户详情组件 -->
		<UserDetail ref="userDetailRef" />
	</div>
</template>

<script setup>
import { getProjectApi, updateProjectStatusApi, getProjectUserListApi } from '@/api/tagger/project';
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, watch } from 'vue';
import { getLabel, gfStatusOptions, projectStatusOptions } from './utils';
import UserDetail from '@/views/tagger/user/user-detail.vue';

const emits = defineEmits(['refresh']);

const props = defineProps({ projectType: Number });
const formLoading = ref(false);
const form = ref({});
const keywordTags = ref([]);
const labelTags = ref([]);
const userList = ref([]);
const userListLoading = ref(false);
const total = ref(0);

// 查询参数
const queryParams = reactive({
	projectId: 0,
	pageNum: 1,
	pageSize: 10,
	keyword: '',
	orderByColumn: '',
	orderByAsc: true,
});

const safeTimes = (a, b) => {
	a = parseFloat(a) ? parseFloat(a) : 0;
	b = parseFloat(b) ? parseFloat(b) : 0;
	return (a * b).toFixed(2);
};

// 弹框数据
const dialogData = reactive({
	isShow: false,
	id: null,
});

// 用户详情组件引用
const userDetailRef = ref(null);

// 获取用户列表数据
const getUserList = () => {
	userListLoading.value = true;
	getProjectUserListApi({
		...queryParams,
		projectId: dialogData.id,
		projectType: props.projectType
	})
		.then((res) => {
			userList.value = res.list || [];
			total.value = res.total || 0;
		})
		.catch((error) => {
			ElMessage.error(`获取用户列表失败: ${error.message || '未知错误'}`);
		})
		.finally(() => {
			userListLoading.value = false;
		});
};

// 分页大小变化
const handleSizeChange = (size) => {
	queryParams.pageSize = size;
	getUserList();
};

// 页码变化
const handleCurrentChange = (page) => {
	queryParams.pageNum = page;
	getUserList();
};

// 获取详情
const getDetails = (id) => {
	formLoading.value = true;
	getProjectApi(id).then((res) => {
		formLoading.value = false;
		form.value = res;
		if (res.projectKeyword) {
			keywordTags.value = res.projectKeyword.split(',').filter((item) => !!item);
		}
		if (res.targetLabel) {
			labelTags.value = res.targetLabel.split(',').filter((item) => !!item);
		}

		// 加载用户列表
		queryParams.projectId = id;
		queryParams.pageNum = 1;
		getUserList();
	});
};

// 查看用户
const checkUser = (row) => {
	userDetailRef.value.openDrawer(row.userId);
};

// 打开弹框
const openDialog = (row) => {
	if (!row?.id) {
		ElMessage.warning('请先选择数据集');
		return;
	}
	dialogData.isShow = true;
	dialogData.id = row.id;
	form.value = {};
	getDetails(row.id);
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
	dialogData.id = null;
	form.value = {};
	keywordTags.value = [];
	userList.value = [];
	total.value = 0;
	queryParams.pageNum = 1;
	queryParams.pageSize = 10;
	queryParams.keyword = '';
	queryParams.projectId = 0;
};

// 处理状态变更
const handleStatusChange = (newStatus) => {
	// 如果是关闭数据集，需要先确认
	if (newStatus === 5) {
		ElMessageBox.confirm('关闭数据集后无法再开放，是否继续?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(() => {
			updateDatasetStatus(newStatus);
		}).catch(() => {
			// 用户取消，恢复原来的状态
			form.value.projectStatus = 4;
			ElMessage.info('已取消关闭操作');
		});
	} else {
		updateDatasetStatus(newStatus);
	}
};

// 更新数据集状态
const updateDatasetStatus = (newStatus) => {
	const statusText = newStatus === 4 ? '开放' : '关闭';
	ElMessage.info(`正在将数据集状态修改为${statusText}...`);

	const updateData = {
		projectId: form.value.id,
		open: newStatus === 4 ? true : false,
	};

	updateProjectStatusApi(updateData)
		.then(() => {
			emits('refresh');
			ElMessage.success(`数据集状态已修改为${statusText}`);
		})
		.catch((error) => {
			// 如果接口调用失败，恢复原来的状态
			form.value.projectStatus = form.value.projectStatus === 4 ? 5 : 4;
			ElMessage.error(`状态修改失败: ${error.message || '未知错误'}`);
		});
};

defineExpose({
	openDialog,
});
</script>

<style scoped>
.section {
	margin-bottom: 24px;
}

.section:last-child {
	margin-bottom: 0;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 16px;
	position: relative;
	padding-left: 12px;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 16px;
	background-color: #409eff;
	border-radius: 2px;
}

.section-content {
	background-color: #f5f7fa;
	border-radius: 4px;
	padding: 16px;
}

.tag-container {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;
}

.image-placeholder {
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #fff;
	border: 1px dashed #dcdfe6;
	border-radius: 4px;
}

.sub-info {
	color: #606266;
	line-height: 1.8;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
