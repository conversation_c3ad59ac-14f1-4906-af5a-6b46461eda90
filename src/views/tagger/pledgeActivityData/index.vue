<template>
	<el-card shadow="never" class="card-box">
		<!-- 统计信息区域 -->
		<div class="statistics-section" v-loading="dataInfoLoading">
			<div class="input-section">
				<div class="label-input">
					<label class="w-40">审核者数量：</label>
					<el-input v-model.number="auditorNum" placeholder="请输入" style="width: 180px" @blur="handleAuditorNumChange" />
				</div>
				<div class="label-result">
					<label>对应可完成审核任务量：</label>
					<span>{{ dataInfo.auditStartNum || 0 }}-{{ dataInfo.auditEndNum || 0 }}</span>
				</div>
			</div>

			<div class="statistics-row">
				<div>对应质押排行前{{ auditorNum }}位的质押总和: {{ dataInfo.totalPledge || 0 }}</div>
				<div>其中已方审核用户数量: {{ dataInfo.selfUserNum || 0 }}</div>
				<div>真实用户数量: {{ dataInfo.realUserNum || 0 }}</div>
				<div>已方质押得到的代币总量占总比例{{ dataInfo.ratio || '0%' }}</div>
			</div>

			<!-- 更新为表单筛选区 -->
			<el-form :model="queryParams" label-width="120px">
				<el-row :gutter="20">
					<el-col :sm="24" :md="12" :lg="8" :xl="6">
						<el-form-item label="用户角色">
							<el-select v-model="queryParams.userRoleId" placeholder="请选择用户角色">
								<el-option label="真实用户" :value="1" />
								<el-option label="己方审核用户" :value="3" />
							</el-select>
						</el-form-item>
					</el-col>
					<el-col :sm="24" :md="12" :lg="8" :xl="6">
						<el-form-item label="钱包地址">
							<el-input v-model="queryParams.walletAddress" @keyup.enter="onSearch" clearable placeholder="请输入钱包地址" />
						</el-form-item>
					</el-col>
					<el-col :sm="24" :md="12" :lg="8" :xl="6">
						<el-form-item label-width="0">
							<el-button type="primary" @click="onSearch">
								<el-icon>
									<Search />
								</el-icon>
								<span class="search-btn__left">查询</span>
							</el-button>
							<el-button @click="onReset">
								<el-icon>
									<Refresh />
								</el-icon>
								<span class="search-btn__left">重置</span>
							</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>
		</div>

		<!-- 表格和下载区域 -->
		<div class="table-section">
			<div class="table-header">
				<el-button type="primary" @click="downloadData"> 下载结果列表至本地 </el-button>
			</div>

			<el-table :data="tableData.data" v-loading="tableData.isLoading" border style="width: 100%">
				<el-table-column type="index" label="排名" width="80" align="center" />
				<el-table-column prop="walletAddress" label="用户钱包地址" min-width="200" align="center" />
				<el-table-column prop="totalPledge" label="总质押量" min-width="120" align="center" />
				<el-table-column prop="walletType" label="钱包类型" min-width="120" align="center" />
				<el-table-column label="是否已方审核用户" min-width="150" align="center">
					<template #default="{ row }">
						{{ row.selfUser === 1 ? '是' : '否' }}
					</template>
				</el-table-column>
				<el-table-column label="当前是否有审核资格" min-width="150" align="center">
					<template #default="{ row }">
						{{ row.canAudit === 1 ? '是' : '否' }}
					</template>
				</el-table-column>
				<el-table-column label="操作" width="120" align="center">
					<template #default="{ row }">
						<el-button link type="primary" @click="viewUser(row)">查看用户</el-button>
					</template>
				</el-table-column>
			</el-table>

			<!-- 分页 -->
			<div class="pagination-container">
				<el-pagination :current-page="queryParams.pageNum" :page-size="queryParams.pageSize" :page-sizes="[10, 20, 50, 100]" :total="total" layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
			</div>
		</div>

		<!-- 用户详情组件 -->
		<UserDetail ref="userDetailRef" />
	</el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { Search, Refresh } from '@element-plus/icons-vue';
import { getLastAuditorNumApi, getPledgeActivityDataInfoApi, getPledgeActivityDataPageApi } from '@/api/tagger/pledgeActivity';
import { getToken } from '@/utils/token';
import UserDetail from '@/views/tagger/user/user-detail.vue';

// 审核者数量
const auditorNum = ref(100);
const dataInfo = ref({});
const dataInfoLoading = ref(false);
const total = ref(0);

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 查询参数
const queryParams = reactive({
	pageNum: 1,
	pageSize: 10,
	keyword: '',
	orderByColumn: '',
	orderByAsc: true,
	userRoleId: 1,
	walletAddress: '',
	taskType: '',
});

// 上一次提交的审核者数量
const lastSubmittedNum = ref(0);

// 用户详情组件引用
const userDetailRef = ref(null);

// 获取最近的审核者数量
const getLastAuditorNum = async () => {
	try {
		const res = await getLastAuditorNumApi();
		if (res) {
			auditorNum.value = res;
		}
	} catch (error) {
		console.error('获取最近审核者数量失败:', error);
	}
};

// 获取数据统计信息
const getDataInfo = async () => {
	if (!auditorNum.value) return;

	dataInfoLoading.value = true;
	try {
		const res = await getPledgeActivityDataInfoApi(auditorNum.value);
		dataInfo.value = res || {};
	} catch (error) {
		console.error('获取统计数据失败:', error);
	} finally {
		dataInfoLoading.value = false;
	}
};

// 获取表格数据
const getTableList = () => {
	if (!auditorNum.value) return;

	tableData.isLoading = true;
	getPledgeActivityDataPageApi({
		...queryParams,
		userRoleId: queryParams.userRoleId,
		auditorNum: auditorNum.value
	})
		.then((res) => {
			// 获取原始列表数据
			let rawList = res.list || [];
			
			// 计算显示数量：服务器返回的total和auditorNum中较小的值
			const displayCount = Math.min(res.total || 0, auditorNum.value);
			
			// 如果原始列表数据超过了displayCount，则截取前displayCount条数据
			if (rawList.length > displayCount) {
				rawList = rawList.slice(0, displayCount);
			}
			
			tableData.data = rawList;
			total.value = displayCount; // 更新分页组件的total为实际显示的数量
		})
		.catch((error) => {
			console.error('获取表格数据失败:', error);
			ElMessage.error('获取表格数据失败');
		})
		.finally(() => {
			tableData.isLoading = false;
		});
};

const getPageInfo = () => {
	getDataInfo();
	getTableList();
};

// 下载数据
const downloadData = () => {
	const url = window.origin + '/api/admin/pledgeActivity/downloadPledgeActivityDataList' + `?userRoleId=${queryParams.userRoleId  || 0}` + `&walletAddress=${queryParams.walletAddress || ''}` + `&Authorization=${getToken()}`;

	window.open(url, '_blank');
};

// 查看用户
const viewUser = (row) => {
	userDetailRef.value.openDrawer(row.userId);
};

// 查询
const onSearch = () => {
	queryParams.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryParams.walletAddress = '';
	queryParams.keyword = '';
	queryParams.orderByColumn = '';
	queryParams.orderByAsc = true;
	queryParams.userRoleId = 1;
	queryParams.taskType = '';
	queryParams.pageNum = 1;
	auditorNum.value = 30
	lastSubmittedNum.value = 30
	getTableList();
};

// 分页大小变化
const handleSizeChange = (size) => {
	queryParams.pageSize = size;
	queryParams.pageNum = 1
	getTableList();
};

// 页码变化
const handleCurrentChange = (page) => {
	queryParams.pageNum = page;
	getTableList();
};

// 处理审核者数量变化
const handleAuditorNumChange = () => {
	// 只有数量有变化时才更新数据
	if (auditorNum.value && auditorNum.value !== lastSubmittedNum.value) {
		lastSubmittedNum.value = auditorNum.value;
		queryParams.pageNum = 1
		getPageInfo();
	}
};

// 初始化
onMounted(() => {
	getLastAuditorNum().then(() => {
		lastSubmittedNum.value = auditorNum.value;
		getPageInfo();
	});
});
</script>

<style scoped>
.card-box {
	margin-bottom: 20px;
}

.statistics-section {
	margin-bottom: 20px;
	padding: 16px;
	background-color: #f8f8f8;
	border-radius: 4px;
}

.input-section {
	display: flex;
	align-items: center;
	margin-bottom: 15px;
}

.label-input {
	display: flex;
	align-items: center;
	margin-right: 20px;
}

.label-result {
	display: flex;
	align-items: center;
}

.statistics-row {
	display: flex;
	flex-wrap: wrap;
	gap: 20px;
	margin-bottom: 15px;
	line-height: 1.6;
}

.table-section {
	margin-top: 20px;
}

.table-header {
	margin-bottom: 15px;
	display: flex;
	justify-content: flex-end;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}

.search-btn__left {
	margin-left: 5px;
}
</style>
