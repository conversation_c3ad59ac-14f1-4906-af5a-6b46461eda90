<template>
	<el-card shadow="never" class="card-box">
		<div class="mb-3 font-bold text-xl">当前{{ configType === 'tag' ? '标注' : '审核' }}白名单共{{ pageData.total }}人</div>
		<el-form :model="queryForm" label-width="120px">
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="钱包地址">
						<el-input v-model="queryForm.walletAddress" @keyup.enter="onSearch" clearable placeholder="请输入钱包地址" />
					</el-form-item>
				</el-col>
				<el-col :lg="8" :md="12" :sm="24" :xl="6">
					<el-form-item label="创建时间">
						<date-picker type="daterange" v-model:startDate="queryForm.createTimeStart" v-model:endDate="queryForm.createTimeEnd" clearable start-placeholder="开始时间" end-placeholder="结束时间" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="搜索">
						<el-input v-model="queryForm.keyword" @keyup.enter="onSearch" clearable placeholder="请输入关键字" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label-width="0">
						<el-button type="primary" @click="onSearch">
							<el-icon>
								<ele-search />
							</el-icon>
							<span class="search-btn__left">查询</span>
						</el-button>
						<el-button @click="onReset">
							<el-icon>
								<ele-refresh />
							</el-icon>
							<span class="search-btn__left">重置</span>
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class="table-btn-box mb-3 flex justify-end">
			<el-button type="danger" :disabled="selectedRows.length === 0" @click="batchDelete">
				<el-icon class="mr-1">
					<ele-delete />
				</el-icon>
				批量删除 ({{ selectedRows.length }})
			</el-button>

			<el-upload :show-file-list="false" :before-upload="() => false" :on-change="handleUpload" accept=".xlsx">
				<el-button type="primary">
					<el-icon class="mr-1">
						<ele-upload />
					</el-icon>
					导入{{ configType === 'tag' ? '标注' : '审核' }}白名单
				</el-button>
			</el-upload>
		</div>
		<el-table ref="tableRef" v-loading="tableData.isLoading" :data="tableData.data" border @selection-change="handleSelectionChange">
			<el-table-column type="selection" width="55" align="center" />
			<el-table-column type="index" label="序号" width="60" align="center" />
			<el-table-column prop="userId" label="userID" align="center" />
			<el-table-column prop="walletAddress" label="钱包地址" align="center" show-overflow-tooltip />
			<el-table-column prop="todayNum" label="今日完成任务数" align="center" />
			<el-table-column prop="totalNum" label="总完成任务数" align="center" />
			<el-table-column prop="createTime" label="创建时间" align="center" />
			<el-table-column label="操作" fixed="right" align="center" width="150">
				<template #default="{ row }">
					<el-button link type="primary" @click="openDetailDialog(row)">详情</el-button>
					<el-button link type="danger" @click="deleteItem(row)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>

		<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

		<!-- 详情弹窗 -->
		<DetailDialog ref="detailDialogRef" :config-type="configType" />

		<!-- 导入弹窗 -->
		<ImportDialog ref="importDialogRef" :config-type="configType" @refresh="getTableList" />
	</el-card>
</template>

<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getWhiteTagConfigPageApi, getWhiteAuditConfigPageApi, deleteWhiteTagConfigApi, deleteWhiteAuditConfigApi, importWhiteTagConfigPageApi, importWhiteAuditConfigPageApi } from '@/api/tagger/whiteConfig';
import DetailDialog from './detail.vue';
import ImportDialog from './import.vue';

const route = useRoute();

// 根据路由判断配置类型
const configType = computed(() => {
	return route.path.endsWith('/tag') ? 'tag' : 'audit';
});

const queryForm = ref({
	walletAddress: '',
	keyword: '',
	createTimeStart: '',
	createTimeEnd: '',
});

// 表格引用和选中行
const tableRef = ref();
const selectedRows = ref([]);

// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {
		walletAddress: '',
		keyword: '',
		createTimeStart: '',
		createTimeEnd: '',
	};
	pageData.pageNum = 1;
	// 清空选中状态
	selectedRows.value = [];
	getTableList();
};

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 获取表格列表
const getTableList = () => {
	tableData.isLoading = true;
	const apiFunction = configType.value === 'tag' ? getWhiteTagConfigPageApi : getWhiteAuditConfigPageApi;

	apiFunction({ ...pageData, ...queryForm.value })
		.then((res) => {
			tableData.data = res?.list || [];
			pageData.total = res?.total || 0;
			tableData.isLoading = false;
		})
		.catch(() => {
			tableData.isLoading = false;
		});
};

// 删除单个项目
const deleteItem = (row) => {
	ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			const deleteApi = configType.value === 'tag' ? deleteWhiteTagConfigApi : deleteWhiteAuditConfigApi;
			deleteApi({ ids: [row.id] }).then(() => {
				ElMessage.success('删除成功');
				getTableList();
				// 清空选中状态
				selectedRows.value = [];
			});
		})
		.catch(() => {});
};

// 批量删除
const batchDelete = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要删除的数据');
		return;
	}

	ElMessageBox.confirm(`是否确认删除选中的 ${selectedRows.value.length} 条数据？`, '批量删除', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			const deleteApi = configType.value === 'tag' ? deleteWhiteTagConfigApi : deleteWhiteAuditConfigApi;
			const ids = selectedRows.value.map((row) => row.id);
			deleteApi({ ids }).then(() => {
				ElMessage.success(`成功删除 ${selectedRows.value.length} 条数据`);
				getTableList();
				// 清空选中状态
				selectedRows.value = [];
			});
		})
		.catch(() => {});
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
	selectedRows.value = selection;
};

// 导入审核机器人逻辑
const handleUpload = async (file) => {
	const formData = new FormData();
	formData.append('file', file.raw);
	try {
		const res = (await configType.value) === 'tag' ? importWhiteTagConfigPageApi(formData) : importWhiteAuditConfigPageApi(formData);
		if (res && res.key) {
			if (res.newNum) {
				await ElMessageBox.confirm(
					`重复地址 ${res.repeatNum} 个，新地址 ${res.newNum} 个。是否确认导入 ${res.newNum} 个新地址？
      （现有${configType.value === 'tag' ? '标注' : '审核'}名单内其他地址仍保有${configType.value === 'tag' ? '标注' : '审核'}资格，如需修改请进行删除操作。）`,
					'批量导入',
					{
						confirmButtonText: '确认',
						cancelButtonText: '取消',
						type: 'warning',
					}
				).then(async () => {
					await importWhiteTagConfigPageApi(res.key);
					ElMessage.success('导入成功');
					getTableList();
				});
			} else {
				ElMessageBox.alert(
					`重复地址 ${res.repeatNum} 个，新地址 ${res.newNum} 个。
      （现有${configType.value === 'tag' ? '标注' : '审核'}名单内其他地址仍保有${configType.value === 'tag' ? '标注' : '审核'}资格，如需修改请进行删除操作。）`,
					'批量导入',
					{
						// if you want to disable its autofocus
						// autofocus: false,
						confirmButtonText: '确定',
					}
				);
			}
		} else {
			ElMessage.error('导入失败，未返回key');
		}
	} catch (e) {
		ElMessage.error('导入失败: ' + (e.message || '未知错误'));
	}
};

// 弹窗引用
const detailDialogRef = ref(null);
const importDialogRef = ref(null);

// 打开详情弹窗
const openDetailDialog = (data = {}) => {
	detailDialogRef.value.openDialog(data);
};

// 打开导入弹窗
const openImportDialog = () => {
	importDialogRef.value.openDialog();
};

// 监听路由变化
watch(
	route,
	() => {
		onReset();
	},
	{
		immediate: true,
	}
);
</script>

<style scoped>
.el-table :deep(.el-table__header) th {
	background-color: #f5f7fa;
	color: #606266;
}

.search-btn__left {
	margin-left: 5px;
}

.mb-3 {
	margin-bottom: 16px;
}

.table-btn-box {
	display: flex;
	gap: 12px;
}
</style>
