<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close :title="`查看${configType === 'tag' ? '标注' : '审核'}白名单配置`" @close="closeDialog" draggable width="600px">
			<div v-loading="formLoading">
				<el-descriptions :column="2" border>
					<el-descriptions-item label="ID">{{ form.id }}</el-descriptions-item>
					<el-descriptions-item label="用户ID">{{ form.userId }}</el-descriptions-item>
					<el-descriptions-item label="钱包地址" :span="2">{{ form.walletAddress }}</el-descriptions-item>
					<el-descriptions-item label="今日完成任务数">{{ form.todayNum || 0 }}</el-descriptions-item>
					<el-descriptions-item label="总完成任务数">{{ form.totalNum || 0 }}</el-descriptions-item>
					<el-descriptions-item label="创建时间">{{ form.createTime }}</el-descriptions-item>
					<el-descriptions-item label="更新时间">{{ form.updateTime }}</el-descriptions-item>
					<el-descriptions-item label="创建人ID">{{ form.createId }}</el-descriptions-item>
					<el-descriptions-item label="更新人ID">{{ form.updateId }}</el-descriptions-item>
				</el-descriptions>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { getWhiteTagConfigApi, getWhiteAuditConfigApi } from '@/api/tagger/whiteConfig';

const props = defineProps({
	configType: {
		type: String,
		required: true,
		validator: (value) => ['tag', 'audit'].includes(value),
	},
});

const formLoading = ref(false);
const form = ref({});

// 弹框数据
const dialogData = reactive({
	isShow: false,
	id: null,
});

// 获取详情
const getDetails = (id) => {
	formLoading.value = true;
	const apiFunction = props.configType === 'tag' ? getWhiteTagConfigApi : getWhiteAuditConfigApi;
	
	apiFunction(id).then((res) => {
		formLoading.value = false;
		form.value = res || {};
	}).catch((error) => {
		formLoading.value = false;
		ElMessage.error(`获取详情失败: ${error.message || '未知错误'}`);
	});
};

// 打开弹框
const openDialog = (row) => {
	if (!row?.id) {
		ElMessage.warning('请先选择数据');
		return;
	}
	dialogData.isShow = true;
	dialogData.id = row.id;
	form.value = {};
	getDetails(row.id);
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
	dialogData.id = null;
	form.value = {};
};

defineExpose({
	openDialog,
});
</script>

<style scoped>
.el-descriptions :deep(.el-descriptions__header) {
	background-color: #f5f7fa;
}
</style>
