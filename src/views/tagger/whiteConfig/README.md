# 白名单配置页面

## 概述
这是一个复用的白名单配置管理页面，支持标注白名单和审核白名单两种类型的配置管理。

## 路由配置
- `/tagger/whiteConfig/tag` - 标注白名单配置列表
- `/tagger/whiteConfig/audit` - 审核白名单配置列表

## 功能特性

### 1. 列表功能
- 支持按钱包地址搜索
- 支持按创建时间范围筛选
- 支持关键字搜索
- 分页显示数据

### 2. 数据操作
- **导入功能**: 支持Excel文件批量导入钱包地址
- **导出功能**: 导出当前筛选条件下的所有数据
- **详情查看**: 查看单条记录的详细信息
- **删除功能**: 删除单条记录

### 3. 导入功能详情
- 支持 `.xlsx` 和 `.xls` 格式文件
- 文件大小限制：10MB
- 支持拖拽上传
- 导入时会检测重复地址并提示用户确认

## 文件结构
```
whiteConfig/
├── index.vue      # 主页面
├── detail.vue     # 详情弹窗
├── import.vue     # 导入弹窗
└── README.md      # 说明文档
```

## 数据字段说明

| 字段名 | 描述 | 类型 |
|--------|------|------|
| id | 主键ID | integer |
| userId | 用户ID | integer |
| walletAddress | 钱包地址 | string |
| todayNum | 今日完成任务数 | integer |
| totalNum | 总完成任务数 | integer |
| createTime | 创建时间 | string(date-time) |
| updateTime | 更新时间 | string(date-time) |
| createId | 创建人ID | integer |
| updateId | 更新人ID | integer |

## API接口
使用 `@/api/tagger/whiteConfig.js` 中定义的接口：

### 标注白名单
- `getWhiteTagConfigPageApi` - 获取分页列表
- `getWhiteTagConfigApi` - 获取详情
- `deleteWhiteTagConfigApi` - 删除配置
- `importWhiteTagConfigPageApi` - 导入配置

### 审核白名单
- `getWhiteAuditConfigPageApi` - 获取分页列表
- `getWhiteAuditConfigApi` - 获取详情
- `deleteWhiteAuditConfigApi` - 删除配置
- `importWhiteAuditConfigPageApi` - 导入配置

## 使用说明

1. **访问页面**: 根据需要访问对应的路由
2. **搜索筛选**: 使用顶部的搜索表单进行数据筛选
3. **导入数据**: 点击"导入白名单"按钮，上传Excel文件
4. **导出数据**: 点击"导出数据"按钮下载当前数据
5. **查看详情**: 点击表格中的"详情"按钮查看完整信息
6. **删除记录**: 点击表格中的"删除"按钮删除记录

## 注意事项
- 页面会根据路由自动判断是标注还是审核白名单
- 导入功能需要后端支持相应的接口
- 导出功能使用浏览器直接下载方式
- 删除操作需要用户确认
