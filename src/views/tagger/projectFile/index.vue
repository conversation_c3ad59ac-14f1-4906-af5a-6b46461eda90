<template>
  <el-card shadow="never" class="card-box">
    <el-form :model="queryForm" label-width="100px">
      <el-row :gutter="20">
      </el-row>
      <el-row :gutter="20">
        <el-col :sm="24" :md="12" :lg="8" :xl="6">
          <el-form-item label="搜索">
            <el-input v-model="queryForm.keyword" @keyup.enter="onSearch" clearable placeholder="请输入名称"/>
          </el-form-item>
        </el-col>
        <el-col :sm="24" :md="12" :lg="8" :xl="6">
          <el-form-item label-width="0">
            <el-button type="primary" @click="onSearch">
              <el-icon>
                <ele-search />
              </el-icon>
              <span class="search-btn__left">查询</span>
            </el-button>
            <el-button @click="onReset">
              <el-icon>
                <ele-refresh />
              </el-icon>
              <span class="search-btn__left">重置</span>
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-auth="'t:project:file:add'" class="table-btn-box mb-3">
      <el-button type="primary" @click="openDialog">
        <el-icon class="mr-1">
          <ele-circle-plus />
          </el-icon>
        新 增
      </el-button>
    </div>
    <el-table
      v-loading="tableData.isLoading"
      :data="tableData.data"
      @sort-change="sortChange"
      border
      row-key="id"
    >
      <el-table-column prop="number" label="序号" align="center" width="60px"/>
      <el-table-column prop="projectId" label="projectId" align="center"/>
      <el-table-column prop="sha256" label="sha256" align="center"/>
      <el-table-column prop="ossKey" label="ossKey" align="center"/>
      <el-table-column prop="name" label="name" align="center"/>
      <el-table-column label="操作" fixed="right" align="center" min-width="120">
        <template #default="{ row }">
          <el-button v-auth="'t:project:file:update'" link type="primary" @click="openDialog(row)">
            <el-icon>
              <ele-edit/>
            </el-icon>
            修改
          </el-button>
          <el-button v-auth="'t:project:file:delete'" link type="primary" @click="delTable(row)">
            <el-icon>
              <ele-delete/>
            </el-icon>
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

    <TableForm ref="tableDialogRef" @refresh="getTableList" />

  </el-card>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { deleteProjectFile, getProjectFilePage } from '@/api/tagger/projectFile';
import TableForm from './form.vue';
import { reactive, ref } from 'vue';


let queryForm = ref({});

// 查询
const onSearch = () => {
  pageData.pageNum = 1;
  getTableList();
};

// 重置
const onReset = () => {
  queryForm.value = {};
  pageData.pageNum = 1;
  getTableList();
};

// 分页数据
const pageData = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
});

// 分页
const changePage = (page,size) => {
  pageData.pageNum = page;
  pageData.pageSize = size;
  getTableList();
};

// 排序
const orderBy = ref({});
const sortChange = ({prop, order}) => {
    if (order) {
        orderBy.value.orderByColumn = prop;
        orderBy.value.orderByAsc = order === "ascending";
    } else {
        orderBy.value = {}
    }
    pageData.pageNum = 1;
    getTableList();
}

// 表格数据
const tableData = reactive({
  data: [],
  isLoading: false,
});

// 获取表格列表
const getTableList = () => {
  tableData.isLoading = true;
  getProjectFilePage({ ...pageData, ...queryForm.value, ...orderBy.value }).then((res) => {
    tableData.data = res?.list?.map((p,i)=>{
      p.number = 1 + i + (pageData.pageNum - 1) * pageData.pageSize;
      return p;
    });
    pageData.total = res?.total;
    tableData.isLoading = false;
  });
};

// 删除列表数据
function delTable(row) {
  ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  })
      .then(() => {
        deleteProjectFile(row.id).then(() => {
          ElMessage.success('删除成功');
          getTableList();
        });
      })
      .catch(() => {});
}

// 新增、编辑弹框
const tableDialogRef = ref(null);
// 打开表格操作弹框
function openDialog(data = {}) {
  tableDialogRef.value.openDialog(data);
}

getTableList();

</script>