<template>
  <div v-if="dialogData.isShow">
    <el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="45%">
      <el-form ref="formRef" :rules="rules" :model="form" label-width="100px" v-loading="formLoading">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="projectId" prop="projectId">
              <el-input-number v-model="form.projectId" placeholder="请输入projectId"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="sha256" prop="sha256">
              <el-input v-model="form.sha256" :maxlength="64" clearable placeholder="请输入sha256"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="ossKey" prop="ossKey">
              <el-input v-model="form.ossKey" :maxlength="100" clearable placeholder="请输入ossKey"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="name" prop="name">
              <el-input v-model="form.name" :maxlength="200" clearable placeholder="请输入name"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="createdAt" prop="createdAt">
              <date-picker type="datetime" v-model="form.createdAt" clearable placeholder="请选择createdAt"/>
             </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="updatedAt" prop="updatedAt">
              <date-picker type="datetime" v-model="form.updatedAt" clearable placeholder="请选择updatedAt"/>
             </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-debounce="submit">确定</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {addProjectFile, getProjectFile, updateProjectFile} from "@/api/tagger/projectFile";
import {ElMessage} from "element-plus";
import {reactive, ref} from "vue";


const formRef = ref();

const emits = defineEmits(['refresh']);

// 表单
let addDefaultForm = {

}
let form = ref({});

let formLoading = ref(false);

// 效验规则
const rules = {
}

// 获取详情
const getDetails = (id) => {
  formLoading.value = true;
  getProjectFile(id).then((res) => {
    formLoading.value = false;
    form.value = Object.assign({}, form.value, res);
  });
};

// 弹框数据
const dialogData = reactive({
  isShow: false,
  title: '新增',
  id: null,
});

// 打开弹框
const openDialog = async (row) => {
  dialogData.isShow = true;
  dialogData.title = '新增项目文件';
  if (row?.id) {
    dialogData.id = row.id;
    dialogData.title = '编辑项目文件';
    form.value = {};
    getDetails(row.id);
  }else{
    form.value = addDefaultForm;
  }
};

// 关闭弹框
const closeDialog = () => {
  dialogData.isShow = false;
  dialogData.id = null;
  formRef.value.resetFields();
};

// 提交
const submit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      if (dialogData.id) {
        updateProjectFile(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      } else {
        addProjectFile(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      }
    }
  })

}

defineExpose({
  openDialog,
});
</script>