<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close title="查看审核用户" @close="closeDialog" draggable width="500px">
			<div v-loading="formLoading">
				<div class="section-content">
						<el-descriptions :column="1" border>
							<el-descriptions-item label="用户ID" :span="5">{{ form.userId }}</el-descriptions-item>
							<el-descriptions-item label="钱包地址" :span="5">{{ form.walletAddress }}</el-descriptions-item>
							<el-descriptions-item label="审核对象类型" :span="5">{{ form.type }}</el-descriptions-item>
							<el-descriptions-item label="角色名称" :span="5">{{ form.roleName }}</el-descriptions-item>
							<el-descriptions-item label="今日完成任务数" :span="5">{{ form.todayFinishAuditTaskNum }}</el-descriptions-item>
							<el-descriptions-item label="总完成任务数	" :span="5">{{ form.totalAuditTaskNum }}</el-descriptions-item>
						</el-descriptions>
					</div>
			</div>
		</el-dialog>
	</div>
</template>

<script setup>
import { getReviewerConfig } from '@/api/tagger/reviewerConfig';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
const formLoading = ref(false);
const form = ref({});

// 弹框数据
const dialogData = reactive({
	isShow: false,
	id: null,
});

// 获取详情
const getDetails = (id) => {
	formLoading.value = true;
	getReviewerConfig(id).then((res) => {
		formLoading.value = false;
		form.value = res;
		form.value.type = form.value.type === 1 ? '审核真实用户' : '审核机器人'
	});
};

// 打开弹框
const openDialog = (row) => {
	if (!row?.id) {
		ElMessage.warning('请先选择审核用户');
		return;
	}
	dialogData.isShow = true;
	dialogData.id = row.id;
	form.value = {};
	getDetails(row.id);
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
	dialogData.id = null;
	form.value = {};
};
defineExpose({
	openDialog,
});
</script>

<style scoped>
.section {
	margin-bottom: 24px;
}

.section:last-child {
	margin-bottom: 0;
}

.section-title {
	font-size: 16px;
	font-weight: bold;
	margin-bottom: 16px;
	position: relative;
	padding-left: 12px;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 4px;
	height: 16px;
	background-color: #409eff;
	border-radius: 2px;
}

.section-content {
	background-color: #f5f7fa;
	border-radius: 4px;
	padding: 16px;
}

.tag-container {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;
}

.image-placeholder {
	width: 100px;
	height: 100px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #fff;
	border: 1px dashed #dcdfe6;
	border-radius: 4px;
}

.sub-info {
	color: #606266;
	line-height: 1.8;
}

.pagination-container {
	margin-top: 20px;
	display: flex;
	justify-content: flex-end;
}
</style>
