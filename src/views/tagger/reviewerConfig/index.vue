<template>
	<el-card shadow="never" class="card-box">
		<el-form :model="queryForm" label-width="100px">
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="审核对象类型">
						<el-select v-model="queryForm.type" clearable placeholder="请选择审核对象类型">
							<el-option label="审核真实用户" :value="1" />
							<el-option label="审核机器人" :value="2" />
						</el-select>
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="钱包地址">
						<el-input v-model="queryForm.walletAddress" @keyup.enter="onSearch" clearable placeholder="请输入钱包地址" />
					</el-form-item>
				</el-col>
			</el-row>
			<el-row :gutter="20">
				<el-col :lg="8" :md="12" :sm="24" :xl="6">
					<el-form-item label="创建时间">
						<date-picker type="daterange" v-model:startDate="queryForm.createTimeStart" v-model:endDate="queryForm.createTimeEnd" clearable start-placeholder="开始时间" end-placeholder="结束时间" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label-width="0">
						<el-button type="primary" @click="onSearch">
							<el-icon>
								<ele-search />
							</el-icon>
							<span class="search-btn__left">查询</span>
						</el-button>
						<el-button @click="onReset">
							<el-icon>
								<ele-refresh />
							</el-icon>
							<span class="search-btn__left">重置</span>
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class="flex justify-end gap-3 my-4">
			<el-button type="danger" :disabled="!multipleSelection.length" @click="batchDelete"> 批量删除 </el-button>
			<el-upload :show-file-list="false" :before-upload="() => false" :on-change="handleUpload.bind(null, 2)" accept=".xlsx">
				<el-button type="primary"> 导入审核机器人 </el-button>
			</el-upload>
			<el-upload :show-file-list="false" :before-upload="() => false" :on-change="handleUpload.bind(null, 1)" accept=".xlsx">
				<el-button type="primary"> 导入审核真实用户 </el-button>
			</el-upload>
		</div>
		<el-table v-loading="tableData.isLoading" :data="tableData.data" :default-sort="{ prop: 'todayFinishAuditTaskNum', order: 'descending' }" @selection-change="handleSelectionChange" @sort-change="sortChange" border row-key="id">
			<el-table-column type="selection" width="55" />
			<el-table-column prop="userId" label="审核用户ID" align="center" />
			<el-table-column prop="walletAddress" label="钱包地址" align="center" />
			<el-table-column prop="roleName" label="用户角色" align="center" />
			<el-table-column prop="type" label="审核对象类型" align="center">
				<template #default="scope">
					<span v-if="scope.row.type == 1">审核真实用户</span>
					<span v-if="scope.row.type == 2">审核机器人</span>
				</template>
			</el-table-column>
			<el-table-column prop="todayFinishAuditTaskNum" label="今日完成任务数" sortable="custom" align="center" />
			<el-table-column prop="totalAuditTaskNum" label="总完成任务数" sortable="custom" align="center" />
			<el-table-column prop="createTime" label="创建时间" sortable="custom" align="center" min-width="150" />
			<el-table-column label="操作" fixed="right" align="center" min-width="120">
				<template #default="{ row }">
					<el-button link type="primary" @click="openDetailDialog(row)"> 详情 </el-button>
					<el-button v-auth="'t:reviewer:config:delete'" link type="danger" @click="delTable(row)">
						<el-icon>
							<ele-delete />
						</el-icon>
						删除
					</el-button>
				</template>
			</el-table-column>
		</el-table>

		<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

		<DetailDialog ref="detailDialogRef" />
	</el-card>
</template>
<script setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { batchDeleteReviewerConfigApi, getReviewerConfigPage, importReviewerConfigApi, confirmImportReviewerConfigApi } from '@/api/tagger/reviewerConfig';
import { reactive, ref, h } from 'vue';
import DetailDialog from './detail.vue';

let queryForm = ref({
  orderByColumn: 'todayFinishAuditTaskNum',
  orderByAsc: false
});
const multipleSelection = ref([]);
const detailDialogRef = ref(null);

// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {};
	pageData.pageNum = 1;
	getTableList();
};

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

// 排序
const orderBy = ref({});
const sortChange = ({ prop, order }) => {
	if (order) {
		orderBy.value.orderByColumn = prop;
		orderBy.value.orderByAsc = order === 'ascending';
	} else {
		orderBy.value = {};
	}
	pageData.pageNum = 1;
	getTableList();
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 获取表格列表
const getTableList = () => {
	tableData.isLoading = true;
	getReviewerConfigPage({ ...pageData, ...queryForm.value, ...orderBy.value }).then((res) => {
		tableData.data = res?.list?.map((p, i) => {
			p.number = 1 + i + (pageData.pageNum - 1) * pageData.pageSize;
			return p;
		});
		pageData.total = res?.total;
		tableData.isLoading = false;
	});
};

const handleSelectionChange = (val) => {
	multipleSelection.value = val;
};

// 删除列表数据
function delTable(row) {
	ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		await batchDeleteReviewerConfigApi([row.id]);
		ElMessage.success('删除成功');
		getTableList();
	});
}

// 删除列表数据
function batchDelete() {
	ElMessageBox.confirm(`是否确认删除选中的 ${multipleSelection.value.length} 条数据？`, '批量删除', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	}).then(async () => {
		await batchDeleteReviewerConfigApi(multipleSelection.value.map((item) => item.id));
		ElMessage.success('删除成功');
		getTableList();
	});
}

// 新增、编辑弹框
const tableDialogRef = ref(null);
// 打开表格操作弹框
function openDialog(data = {}) {
	tableDialogRef.value.openDialog(data);
}

// 导入审核机器人逻辑
const handleUpload = async (type, file) => {
	const formData = new FormData();
	formData.append('file', file.raw);
	try {
		const res = await importReviewerConfigApi(type, formData); // 2为机器人
		if (res && res.key) {
			if (res.newNum) {
				await ElMessageBox.confirm(
					`重复地址 ${res.repeatNum} 个，新地址 ${res.newNum} 个。是否确认导入 ${res.newNum} 个新地址？
      （现有审核名单内其他地址仍保有审核资格，如需修改请进行删除操作。）`,
					'批量导入',
					{
						confirmButtonText: '确认',
						cancelButtonText: '取消',
						type: 'warning',
					}
				).then(async () => {
					await confirmImportReviewerConfigApi(res.key);
					ElMessage.success('导入成功');
					getTableList();
				});
			} else {
				ElMessageBox.alert(
					`重复地址 ${res.repeatNum} 个，新地址 ${res.newNum} 个。
      （现有审核名单内其他地址仍保有审核资格，如需修改请进行删除操作。）`,
					'批量导入',
					{
						// if you want to disable its autofocus
						// autofocus: false,
						confirmButtonText: '确定',
					}
				);
			}
		} else {
			ElMessage.error('导入失败，未返回key');
		}
	} catch (e) {
		ElMessage.error('导入失败: ' + (e.message || '未知错误'));
	}
};

function openDetailDialog(data = {}) {
	detailDialogRef.value.openDialog(data);
}

getTableList();
</script>

<style scoped></style>
