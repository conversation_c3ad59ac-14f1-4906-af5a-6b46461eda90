<template>
  <div v-if="dialogData.isShow">
    <el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="45%">
      <el-form ref="formRef" :rules="rules" :model="form" label-width="100px" v-loading="formLoading">
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="审核用户ID" prop="userId">
              <el-input-number v-model="form.userId" placeholder="请输入审核用户ID"  :controls="false" :min="0"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="钱包地址" prop="walletAddress">
              <el-input v-model="form.walletAddress" :maxlength="200" clearable placeholder="请输入钱包地址"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="审核对象类型" prop="type">
              <el-select v-model="form.type" clearable placeholder="请选择审核对象类型">
                <el-option label="审核真实用户" :value="1" />
                <el-option label="审核机器人" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-debounce="submit">确定</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import {addReviewerConfig, getReviewerConfig, updateReviewerConfig} from "@/api/tagger/reviewerConfig";
import {ElMessage} from "element-plus";
import {reactive, ref} from "vue";


const formRef = ref();

const emits = defineEmits(['refresh']);

// 表单
let addDefaultForm = {

}
let form = ref({});

let formLoading = ref(false);

// 效验规则
const rules = {
      userId: [
        {required: true, message: '请输入审核用户ID', trigger: 'blur'},
      ],
      type: [
        {required: true, message: '请选择审核对象类型', trigger: 'change'},
      ],
}

// 获取详情
const getDetails = (id) => {
  formLoading.value = true;
  getReviewerConfig(id).then((res) => {
    formLoading.value = false;
    form.value = Object.assign({}, form.value, res);
  });
};

// 弹框数据
const dialogData = reactive({
  isShow: false,
  title: '新增',
  id: null,
});

// 打开弹框
const openDialog = async (row) => {
  dialogData.isShow = true;
  dialogData.title = '新增审核人员配置';
  if (row?.id) {
    dialogData.id = row.id;
    dialogData.title = '编辑审核人员配置';
    form.value = {};
    getDetails(row.id);
  }else{
    form.value = addDefaultForm;
  }
};

// 关闭弹框
const closeDialog = () => {
  dialogData.isShow = false;
  dialogData.id = null;
  formRef.value.resetFields();
};

// 提交
const submit = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      if (dialogData.id) {
        updateReviewerConfig(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      } else {
        addReviewerConfig(form.value).then(() => {
          ElMessage.success('操作成功');
          closeDialog();
          emits('refresh');
        })
      }
    }
  })

}

defineExpose({
  openDialog,
});
</script>
