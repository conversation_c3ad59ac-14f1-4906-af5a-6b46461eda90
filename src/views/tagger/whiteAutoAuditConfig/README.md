# 审核白名单自动审核配置页面

## 概述
这是一个审核白名单自动审核配置管理页面，用于管理嘉年华审核白名单的自动审核配置。

## 功能特性

### 1. 统计信息展示
- 每个嘉年华审核地址每天可以完成审核任务数量上限
- 需完成审核的标签数
- 每个审核任务选择审核"通过"的概率
- UG钱包今日已完成审核任务数量
- UG钱包今日已完成审核任务数量上限

### 2. 审核控制
- 开启审核功能
- 停止审核功能

### 3. 列表功能
- 支持按钱包地址搜索
- 支持按创建时间范围筛选
- 分页显示数据
- 批量删除功能
- 导入嘉年华审核白名单

### 4. 设置功能
- 配置每日审核任务数量上限
- 配置需完成审核的标签数
- 配置审核通过概率

## 数据字段说明

| 字段名 | 描述 | 类型 |
|--------|------|------|
| userId | 用户ID | integer |
| walletAddress | 钱包地址 | string |
| todayCompletedCount | 今日完成任务数 | integer |
| completedTaskCount | 总完成任务数 | integer |
| createTime | 创建时间 | string(date-time) |

## API接口
使用 `@/api/tagger/whiteConfig.js` 中定义的接口：

- `getWhiteAutoAuditConfigPageApi` - 获取分页列表
- `getWhiteAutoAuditConfigApi` - 获取配置详情
- `updateWhiteAutoAuditConfigApi` - 更新配置
- `deleteWhiteAutoAuditConfigApi` - 删除配置
- `importWhiteAutoAuditConfigPageApi` - 导入配置

## 使用说明

1. **查看统计信息**: 页面顶部显示当前的配置统计信息
2. **设置配置**: 点击"设置"按钮打开配置弹窗，可以修改各项参数
3. **审核控制**: 使用"开启审核"和"停止审核"按钮控制审核状态
4. **搜索筛选**: 使用顶部的搜索表单进行数据筛选
5. **导入数据**: 点击"导入嘉年华审核白名单"按钮，上传Excel文件
6. **批量删除**: 选中记录后点击"批量删除"按钮
7. **查看详情**: 点击表格中的"详情"按钮查看完整信息
8. **删除记录**: 点击表格中的"删除"按钮删除单条记录

## 注意事项
- 导入功能支持Excel格式文件
- 删除操作需要用户确认
- 设置修改后会立即生效
- 审核控制功能需要后端支持相应的接口
