<template>
	<el-card shadow="never" class="card-box">
		<!-- 统计信息 -->
		<div class="statistics">
			<div class="statistics-row" v-loading="statisticsLoading">
				<div class="statistics-item">
					<span class="statistics-label">每个嘉年华审核地址每天可以完成审核任务数量上限：</span>
					<span class="statistics-value">{{ statistics.maxAuditNum || 0 }}</span>
				</div>
				<div class="statistics-item">
					<span class="statistics-label">需完成审核的标签数：</span>
					<span class="statistics-value">{{ statistics.needAuditTagNum || 0 }}</span>
				</div>
				<div class="statistics-item">
					<span class="statistics-label">每个审核任务选择审核"通过"的概率：</span>
					<span class="statistics-value">{{ statistics.passRate || 0 }}%</span>
					<el-button type="primary" size="small" @click="openSettingsDialog" class="ml-2">设置</el-button>
				</div>
			</div>
			<!-- 第二行统计信息 -->
			<div class="statistics-row">
				<div class="statistics-item">
					<span class="statistics-label">UG钱包今日已完成审核任务数量：</span>
					<span class="statistics-value">{{ 0 }}</span>
				</div>
				<div class="statistics-item">
					<span class="statistics-label">UG钱包今日已完成审核任务数量上限：</span>
					<span class="statistics-value">{{ 0 }}</span>
				</div>
				<div class="statistics-item">
					<el-button type="danger" @click="openAudit">开启审核</el-button>
					<el-button @click="stopAudit">停止审核</el-button>
				</div>
			</div>
		</div>

		<el-form class="my-8" :model="queryForm" label-width="120px">
			<el-row :gutter="20">
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label="钱包地址">
						<el-input v-model="queryForm.walletAddress" @keyup.enter="onSearch" clearable placeholder="请输入钱包地址" />
					</el-form-item>
				</el-col>
				<el-col :lg="8" :md="12" :sm="24" :xl="6">
					<el-form-item label="创建时间">
						<date-picker type="daterange" v-model:startDate="queryForm.createTimeStart" v-model:endDate="queryForm.createTimeEnd" clearable start-placeholder="开始时间" end-placeholder="结束时间" />
					</el-form-item>
				</el-col>
				<el-col :sm="24" :md="12" :lg="8" :xl="6">
					<el-form-item label-width="0">
						<el-button type="primary" @click="onSearch">
							<el-icon>
								<ele-search />
							</el-icon>
							<span class="search-btn__left">查询</span>
						</el-button>
						<el-button @click="onReset">
							<el-icon>
								<ele-refresh />
							</el-icon>
							<span class="search-btn__left">重置</span>
						</el-button>
					</el-form-item>
				</el-col>
			</el-row>
		</el-form>
		<div class="table-btn-box mb-3 flex justify-end">
			<el-button type="danger" :disabled="selectedRows.length === 0" @click="batchDelete">
				<el-icon class="mr-1">
					<ele-delete />
				</el-icon>
				批量删除 ({{ selectedRows.length }})
			</el-button>

			<el-upload :show-file-list="false" :before-upload="() => false" :on-change="handleUpload" accept=".xlsx">
				<el-button type="primary">
					<el-icon class="mr-1">
						<ele-upload />
					</el-icon>
					导入嘉年华审核白名单
				</el-button>
			</el-upload>
		</div>
		<el-table ref="tableRef" v-loading="tableData.isLoading" :data="tableData.data" border @selection-change="handleSelectionChange">
			<el-table-column type="selection" width="55" align="center" />
			<el-table-column type="index" label="序号" width="60" align="center" />
			<el-table-column prop="userId" label="userID" align="center" />
			<el-table-column prop="walletAddress" label="钱包地址" align="center" show-overflow-tooltip />
			<el-table-column prop="todayNum" label="今日完成任务数" align="center" />
			<el-table-column prop="totalNum" label="总完成任务数" align="center" />
			<el-table-column prop="createTime" label="创建时间" align="center" />
			<el-table-column label="操作" fixed="right" align="center" width="150">
				<template #default="{ row }">
					<el-button link type="primary" @click="openDetailDialog(row)">详情</el-button>
					<el-button link type="danger" @click="deleteItem(row)">删除</el-button>
				</template>
			</el-table-column>
		</el-table>

		<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

		<!-- 详情弹窗 -->
		<DetailDialog ref="detailDialogRef" config-type="audit" />
		<!-- 设置弹窗 -->
		<el-dialog v-model="settingsDialog.visible" title="设置" width="700px" :close-on-click-modal="false">
			<el-form :model="settingsForm" label-width="370px" :rules="settingsRules" class="p-4" ref="settingsFormRef">
				<el-form-item label="每个嘉年华审核地址每天可以完成审核任务数量上限" prop="maxAuditNum">
					<el-input-number v-model="settingsForm.maxAuditNum" placeholder="请输入审核任务数量上限" />
				</el-form-item>
				<el-form-item label="需完成审核的标签数" prop="needAuditTagNum">
					<el-input-number v-model="settingsForm.needAuditTagNum" placeholder="请输入标注任务数" />
				</el-form-item>
				<el-form-item label='每个审核任务选择审核"通过"的概率' prop="passRate">
					<div class="flex items-center">
						<el-input-number v-model="settingsForm.passRate" placeholder="请输入单个审核任务通过概率" class="mr-2" />
						<span>%</span>
					</div>
				</el-form-item>
			</el-form>
			<template #footer>
				<div class="dialog-footer">
					<el-button @click="settingsDialog.visible = false" :disabled="settingsDialog.loading">取消</el-button>
					<el-button type="primary" @click="saveSettings" :loading="settingsDialog.loading">确定</el-button>
				</div>
			</template>
		</el-dialog>
	</el-card>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getWhiteAutoAuditConfigApi, updateWhiteAutoAuditConfigApi, getWhiteAuditConfigPageApi, deleteWhiteAuditConfigApi, importWhiteAuditConfigPageApi, confirmImportWhiteAuditConfigPageApi, endAutoAuditApi, startAutoAuditApi } from '@/api/tagger/whiteConfig';
import DetailDialog from './detail.vue';

// 统计信息
const statisticsLoading = ref(false);
const statistics = reactive({
	id: 1,
	maxAuditNum: 0,
	needAuditTagNum: 0,
	passRate: 0,
	createId: 0,
	createTime: '',
	updateId: 0,
	updateTime: '',
});

// 查询表单
const queryForm = ref({
	walletAddress: '',
	keyword: '',
	createTimeStart: '',
	createTimeEnd: '',
});
// 表格引用和选中行
const tableRef = ref();
const selectedRows = ref([]);

// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {
		walletAddress: '',
		keyword: '',
		createTimeStart: '',
		createTimeEnd: '',
	};
	pageData.pageNum = 1;
	// 清空选中状态
	selectedRows.value = [];
	getTableList();
};

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});
// 设置弹窗
const settingsDialog = reactive({
	visible: false,
});

const settingsForm = reactive({
	maxAuditNum: statistics.maxAuditNum,
	needAuditTagNum: statistics.needAuditTagNum,
	passRate: statistics.passRate,
});

const settingsRules = {
	maxAuditNum: [{ required: true, message: '请输入审核任务数量上限', trigger: 'blur' }],
	needAuditTagNum: [{ required: true, message: '请输入标注任务数', trigger: 'blur' }],
	passRate: [{ required: true, message: '请输入通过概率', trigger: 'blur' }],
};

const settingsFormRef = ref();

// 获取表格列表
const getTableList = () => {
	tableData.isLoading = true;
	getWhiteAuditConfigPageApi({ ...pageData, ...queryForm.value })
		.then((res) => {
			tableData.data = res?.list || [];
			pageData.total = res?.total || 0;
			tableData.isLoading = false;
		})
		.catch(() => {
			tableData.isLoading = false;
		});
};

// 删除单个项目
const deleteItem = (row) => {
	ElMessageBox.confirm('是否确认删除本条数据？', '提示', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(() => {
			deleteWhiteAuditConfigApi({ ids: [row.id] }).then(() => {
				ElMessage.success('删除成功');
				getTableList();
				// 清空选中状态
				selectedRows.value = [];
			});
		})
		.catch(() => {});
};

// 批量删除
const batchDelete = () => {
	if (selectedRows.value.length === 0) {
		ElMessage.warning('请先选择要删除的数据');
		return;
	}

	ElMessageBox.confirm(`是否确认删除选中的 ${selectedRows.value.length} 条数据？`, '批量删除', {
		confirmButtonText: '确认',
		cancelButtonText: '取消',
		type: 'warning',
	})
		.then(async () => {
			const ids = selectedRows.value.map((row) => row.id);
			await deleteWhiteAuditConfigApi({ ids }).then(() => {
				ElMessage.success(`成功删除 ${selectedRows.value.length} 条数据`);
				getTableList();
				// 清空选中状态
				selectedRows.value = [];
			});
		})
		.catch(() => {});
};

// 处理表格选择变化
const handleSelectionChange = (selection) => {
	selectedRows.value = selection;
};

// 导入审核机器人逻辑
const handleUpload = async (file) => {
	const formData = new FormData();
	formData.append('file', file.raw);
	const res = await importWhiteAuditConfigPageApi(formData);

	if (res.newNum) {
		await ElMessageBox.confirm(
			`重复地址 ${res.repeatNum} 个，新地址 ${res.newNum} 个。是否确认导入 ${res.newNum} 个新地址？
      （现有嘉年华名单内其他地址仍保有嘉年华资格，如需修改请进行删除操作。）`,
			'批量导入',
			{
				confirmButtonText: '确认',
				cancelButtonText: '取消',
				type: 'warning',
			}
		).then(async () => {
			await confirmImportWhiteAuditConfigPageApi(res.key);
			ElMessage.success('导入成功');
			getTableList();
		});
	} else {
		ElMessageBox.alert(
			`重复地址 ${res.repeatNum} 个，新地址 ${res.newNum} 个。
      （现有嘉年华名单内其他地址仍保有嘉年华资格，如需修改请进行删除操作。）`,
			'批量导入',
			{
				// if you want to disable its autofocus
				// autofocus: false,
				confirmButtonText: '确定',
			}
		);
	}
};

// 弹窗引用
const detailDialogRef = ref(null);
const importDialogRef = ref(null);

// 打开详情弹窗
const openDetailDialog = (data = {}) => {
	detailDialogRef.value.openDialog(data);
};

// 打开设置弹窗
const openSettingsDialog = () => {
	settingsForm.maxAuditNum = statistics.maxAuditNum;
	settingsForm.needAuditTagNum = statistics.needAuditTagNum;
	settingsForm.passRate = statistics.passRate;
	settingsDialog.visible = true;
};

// 保存设置
const saveSettings = async () => {
	try {
		await settingsFormRef.value.validate();

		settingsDialog.loading = true;

		const params = {
			id: 1,
			maxAuditNum: parseInt(settingsForm.maxAuditNum),
			needAuditTagNum: parseInt(settingsForm.needAuditTagNum),
			passRate: parseFloat(settingsForm.passRate),
		};

		await updateWhiteAutoAuditConfigApi(params);
		await getSettings();

		// 更新统计信息
		Object.assign(statistics, params);

		ElMessage.success('设置保存成功');
		settingsDialog.visible = false;
	} catch (error) {
		console.error('保存设置失败:', error);
		ElMessage.error('保存设置失败');
	} finally {
		settingsDialog.loading = false;
	}
};

// 开启审核
const openAudit = async () => {
	await startAutoAuditApi();
	ElMessage.success('审核已开启');
};

// 停止审核
const stopAudit = async () => {
	await endAutoAuditApi();
	ElMessage.success('审核已停止');
};

const getSettings = async () => {
	const res = await getWhiteAutoAuditConfigApi(1);

	Object.assign(statistics, res);
	settingsForm.maxAuditNum = statistics.maxAuditNum;
	settingsForm.needAuditTagNum = statistics.needAuditTagNum;
	settingsForm.passRate = statistics.passRate;
};

onMounted(() => {
	getTableList();
	getSettings();
});
</script>

<style scoped>
.statistics {
	background-color: #f5f7fa;
	padding: 15px;
	border-radius: 4px;
	border-left: 4px solid #409eff;
}

.statistics-row {
	display: flex;
	align-items: center;
	gap: 20px;
	margin-bottom: 20px;
	background-color: #f5f7fa;
	border-radius: 4px;
}

.statistics-item {
	display: flex;
	align-items: center;
}

.statistics-label {
	color: #606266;
	margin-right: 8px;
}

.statistics-value {
	font-weight: bold;
	color: #303133;
}

.table-btn-box {
	display: flex;
	gap: 10px;
}

.dialog-footer {
	text-align: right;
}
</style>
