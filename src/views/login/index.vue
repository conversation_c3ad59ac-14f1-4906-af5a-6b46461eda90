<script setup>
import { reactive, ref, onMounted } from 'vue';
import { NextLoading } from '@/utils/loading';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store/modules/user';
import md5 from 'js-md5';

const router = useRouter();

let loginLoading = ref(false);

const state = reactive({
	rules: {
		password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
		username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
	},
});

/**
 * 登录
 * */
const formRef = ref(null);
const form = ref({
	username: '',
	password: '',
});
// 登录
const login = async () => {
	const valid = await formRef.value.validate();
	if (!valid) return;
	loginLoading.value = true;
	useUserStore()
		.login({
			username: form.value.username,
			password: md5(form.value.password),
		})
		.then(async (res) => {
			loginLoading.value = false;

			if (res.token) {
				NextLoading.start();
				const redirect = router.currentRoute.value.query.redirect;
				if (redirect) {
					router.push(redirect)
              .then(()=>{
                  // 重新刷新页面
                  location.reload()
              });
				} else {
					router.push('/')
              .then(()=>{
                  // 重新刷新页面
                  location.reload()
              });
				}
			}
		})
		.catch(() => {
			loginLoading.value = false;
		});
};

// 页面加载时
onMounted(() => {
	NextLoading.done();
});
</script>

<template>
	<div class="main">
		<p class="flex items-center logo-box">
			<img class="logo-img" src="@/assets/logo.png" alt="" />
			<span class="logo-text">Tagger运营平台</span>
		</p>
		<div class="form-box">
			<div class="login-form form">
				<h1 class="title">欢迎登录Tagger运营平台</h1>
				<el-form ref="formRef" size="large" :rules="state.rules" label-position="top" :model="form">
					<el-form-item label="账号" prop="username">
						<el-input v-model="form.username" placeholder="请输入账号" />
					</el-form-item>
					<el-form-item label="登录密码" prop="password">
						<el-input v-model="form.password" type="password" placeholder="请输入密码" show-password auto-complete="new-password"/>
					</el-form-item>
					<el-button type="primary" size="large" auto-insert-space class="w-full mt-6 login-btn" :loading="loginLoading" :disabled="loginLoading" @click="login">登录</el-button>
				</el-form>
			</div>
		</div>
	</div>
</template>

<style scoped lang="scss">
.main {
	background: #c3e4fe url('@/assets/login-bg.png') no-repeat center;
	background-size: cover;
	height: 100vh;
	padding-right: 150px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	transition: all 0.5s;
	.form-box {
		width: 400px;
		background-color: #fff;
		border-radius: 10px;
		box-shadow: 2px 4px 4px 2px rgba(0, 0, 0, 0.05);
		.title {
			font-size: 22px;
			font-weight: 600;
			cursor: pointer;
			margin-bottom: 30px;
		}
		.form {
			padding: 50px 40px;
		}
		.no-active {
			color: #b7babf;
		}
	}
	.login-form {
		font-size: 12px;
		.login-btn {
			background: linear-gradient(90deg, #2e5cf6 0%, #2952dd 100%);
		}
	}
}
.logo-box {
	position: absolute;
	left: 50px;
	top: 40px;
	.logo-img {
		width: 40px;
		margin-right: 5px;
    border-radius: 2px
  ;
	}
	.logo-text {
		font-size: 24px;
		font-weight: 600;
	}
}
:deep(*) {
	.el-input-group__append {
		background-color: transparent;
		padding: 0 10px;
		font-size: 12px;
	}
	.el-form-item__label {
		font-weight: 600;
		color: #000;
	}
	.el-form-item__label::before {
		display: none;
	}
}
</style>
