<template>
	<div v-if="dialogData.isShow">
		<el-dialog v-model="dialogData.isShow" destroy-on-close :title="dialogData.title" @close="closeDialog" draggable width="60%">
			<el-form :model="queryForm" label-width="100px">
				<el-row :gutter="20">
					<el-col :span="14">
						<el-form-item label="搜索">
							<el-input v-model="queryForm.keyword" @keyup.enter="onSearch" clearable placeholder="请输入用户昵称" />
						</el-form-item>
					</el-col>
					<el-col :sm="24" :md="12" :lg="8" :xl="6">
						<el-form-item label-width="0">
							<el-button type="primary" @click="onSearch">
								<el-icon>
									<ele-search />
								</el-icon>
								<span class="search-btn__left">查询</span>
							</el-button>
							<el-button @click="onReset">
								<el-icon>
									<ele-refresh />
								</el-icon>
								<span class="search-btn__left">重置</span>
							</el-button>
						</el-form-item>
					</el-col>
				</el-row>
			</el-form>

			<el-table v-loading="tableData.isLoading" :data="tableData.data" @sort-change="sortChange" border row-key="id">
				<el-table-column prop="id" label="用户ID" align="center" />
				<el-table-column prop="head" label="头像" align="center">
					<template #default="scope">
						<img class="user-avatar" v-if="scope.row.head" :src="scope.row.head" alt="" />
					</template>
				</el-table-column>
				<el-table-column prop="nickname" label="昵称" align="center" min-width="120" />
				<el-table-column prop="roleName" label="角色" align="center" min-width="120" />
				<el-table-column prop="createTime" label="创建时间" align="center" min-width="130" />
				<el-table-column label="操作" fixed="right" align="center" min-width="90">
					<template #default="{ row }">
						<el-button link type="primary" @click="choiceSysUser(row)"> 选择</el-button>
					</template>
				</el-table-column>
			</el-table>

			<Pagination v-model:currentPage="pageData.pageNum" v-model:pageSize="pageData.pageSize" :total="pageData.total" @change="changePage" />

			<template #footer>
				<div class="dialog-footer">
					<el-button @click="closeDialog">取消</el-button>
				</div>
			</template>
		</el-dialog>
	</div>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { deleteSysUserApi, getSysUserPageApi } from '@/api/user';

const emits = defineEmits(['choice']);

let queryForm = ref({});

// 查询
const onSearch = () => {
	pageData.pageNum = 1;
	getTableList();
};

// 重置
const onReset = () => {
	queryForm.value = {};
	pageData.pageNum = 1;
	getTableList();
};

// 弹框数据
const dialogData = reactive({
	isShow: false,
	title: '新增',
	id: null,
});

// 打开弹框
const openDialog = async () => {
	dialogData.isShow = true;
	dialogData.title = '选择系统用户';
	getTableList();
};

// 关闭弹框
const closeDialog = () => {
	dialogData.isShow = false;
};

// 表格数据
const tableData = reactive({
	data: [],
	isLoading: false,
});

// 分页数据
const pageData = reactive({
	pageNum: 1,
	pageSize: 10,
	total: 0,
});

const getTableList = () => {
	tableData.isLoading = true;
	getSysUserPageApi({ ...pageData, ...queryForm.value }).then((res) => {
		tableData.data = res?.list?.map((p, i) => {
			p.number = 1 + i + (pageData.pageNum - 1) * pageData.pageSize;
			return p;
		});
		pageData.total = res?.total;
		tableData.isLoading = false;
	});
};

// 分页
const changePage = (page, size) => {
	pageData.pageNum = page;
	pageData.pageSize = size;
	getTableList();
};

const choiceSysUser = (row) => {
	closeDialog();
	emits('choice', row);
};

defineExpose({
	openDialog,
});
</script>

<style lang="scss" scoped>
.user-avatar {
	width: 40px;
	border-radius: 6px;
	display: block;
	margin: 0 auto;
}
</style>
