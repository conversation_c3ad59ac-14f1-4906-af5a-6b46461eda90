{"name": "spring-boot-plus-vue3", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "lint:prettier": "prettier --write \"**/*.{js,ts,json,css,less,scss,vue,html,md}\""}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@highlightjs/vue-plugin": "^2.1.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "autoprefixer": "^10.4.19", "axios": "^1.6.8", "dayjs": "^1.11.10", "echarts": "^5.5.1", "element-china-area-data": "^6.1.0", "element-plus": "^2.9.4", "highlight.js": "^11.9.0", "js-cookie": "^3.0.5", "js-md5": "^0.8.3", "lodash-es": "^4.17.21", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persist": "^1.0.0", "postcss": "^8.4.38", "qs": "^6.12.1", "tailwindcss": "^3.4.3", "vue": "^3.4.21", "vue-clipboard3": "^2.0.0", "vue-router": "^4.3.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "eslint": "8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.25.0", "prettier": "^3.2.5", "sass": "^1.75.0", "sass-loader": "^14.2.1", "unplugin-vue-components": "^0.26.0", "vite": "^5.2.0", "vite-plugin-svg-icons": "^2.0.1"}, "engines": {"node": ">=18.0.0"}, "volta": {"node": "18.20.2"}, "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af"}